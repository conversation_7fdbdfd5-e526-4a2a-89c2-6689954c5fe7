{"configurations": [{"cStandard": "c99", "compilerPath": "C:/JL/pi32/bin/clang.exe", "cppStandard": "c++11", "defines": ["SUPPORT_MS_EXTENSIONS", "CONFIG_CPU_BR23", "CONFIG_RELEASE_ENABLE", "CONFIG_EQ_SUPPORT_ASYNC", "EQ_CORE_V2", "CONFIG_MIXER_CYCLIC", "CONFIG_FREE_RTOS_ENABLE", "SUPPORT_BLUETOOTH_PROFILE_RELEASE", "CONFIG_SOUNDBOX", "EVENT_HANDLER_NUM_CONFIG=2", "EVENT_TOUCH_ENABLE_CONFIG=0", "EVENT_POOL_SIZE_CONFIG=256", "CONFIG_EVENT_KEY_MAP_ENABLE=0", "TIMER_POOL_NUM_CONFIG=15", "APP_ASYNC_POOL_NUM_CONFIG=0", "VFS_ENABLE=1", "USE_SDFILE_NEW=1", "SDFILE_STORAGE=1", "VFS_FILE_POOL_NUM_CONFIG=1", "FS_VERSION=0x020001", "FATFS_VERSION=0x020101", "SDFILE_VERSION=0x020000", "VM_MAX_SIZE_CONFIG=64*1024", "VM_ITEM_MAX_NUM=256", "CONFIG_TWS_ENABLE", "CONFIG_SOUNDBOX_CASE_ENABLE", "CONFIG_NEW_CFG_TOOL_ENABLE", "AUDIO_REC_POOL_NUM=1", "AUDIO_DEC_POOL_NUM=3", "AUDIO_LOCAL_DEC_USE_MALLOC", "CONFIG_TONE_LOCK_BY_BT_TIME", "CONFIG_LMP_CONN_SUSPEND_ENABLE", "CONFIG_BTCTRLER_TASK_DEL_ENABLE", "CONFIG_UPDATA_ENABLE", "CONFIG_OTA_UPDATA_ENABLE", "CONFIG_ITEM_FORMAT_VM", "CONFIG_DNS_ENABLE", "CONFIG_MMU_ENABLE", "CONFIG_SBC_CODEC_HW", "CONFIG_MSBC_CODEC_HW", "CONFIG_AEC_M=640", "CONFIG_AUDIO_ONCHIP", "CONFIG_MEDIA_DEVELOP_ENABLE", "CONFIG_MEDIA_EFFECT_DISABLE", "__GCC_PI32V2__", "CONFIG_NEW_ECC_ENABLE", "__PI32V2__"], "includePath": ["${workspaceFolder}/include_lib", "${workspaceFolder}/include_lib/driver", "${workspaceFolder}/include_lib/driver/device", "${workspaceFolder}/include_lib/driver/cpu/br23", "${workspaceFolder}/include_lib/system", "${workspaceFolder}/include_lib/system/generic", "${workspaceFolder}/include_lib/system/device", "${workspaceFolder}/include_lib/system/fs", "${workspaceFolder}/include_lib/system/ui", "${workspaceFolder}/include_lib/btctrler", "${workspaceFolder}/include_lib/btctrler/port/br23", "${workspaceFolder}/include_lib/update", "${workspaceFolder}/include_lib/agreement", "${workspaceFolder}/include_lib/btstack/third_party/common", "${workspaceFolder}/include_lib/btstack/third_party/rcsp", "${workspaceFolder}/include_lib/media/media_develop", "${workspaceFolder}/include_lib/media/media_develop/media", "${workspaceFolder}/include_lib/media/media_develop/media/cpu/br23", "${workspaceFolder}/include_lib/media/media_develop/media/cpu/br23/asm", "${workspaceFolder}/cpu/br23/audio_mic", "${workspaceFolder}/apps/soundbox/include", "${workspaceFolder}/apps/soundbox/include/task_manager", "${workspaceFolder}/apps/soundbox/include/task_manager/bt", "${workspaceFolder}/apps/soundbox/include/user_api", "${workspaceFolder}/apps/common", "${workspaceFolder}/apps/common/device", "${workspaceFolder}/apps/common/audio", "${workspaceFolder}/apps/common/audio/stream", "${workspaceFolder}/apps/common/power_manage", "${workspaceFolder}/apps/common/charge_box", "${workspaceFolder}/apps/common/third_party_profile/common", "${workspaceFolder}/apps/common/third_party_profile/interface", "${workspaceFolder}/apps/common/third_party_profile/jieli", "${workspaceFolder}/apps/common/third_party_profile/jieli/trans_data_demo", "${workspaceFolder}/apps/common/third_party_profile/jieli/online_db", "${workspaceFolder}/apps/common/third_party_profile/jieli/JL_rcsp", "${workspaceFolder}/apps/common/third_party_profile/jieli/JL_rcsp/rcsp_updata", "${workspaceFolder}/apps/common/third_party_profile/jieli/JL_rcsp/bt_trans_data", "${workspaceFolder}/apps/common/third_party_profile/jieli/JL_rcsp/adv_rcsp_protocol", "${workspaceFolder}/apps/common/third_party_profile/jieli/JL_rcsp/adv_app_setting", "${workspaceFolder}/apps/common/dev_manager", "${workspaceFolder}/apps/common/file_operate", "${workspaceFolder}/apps/common/music", "${workspaceFolder}/apps/common/include", "${workspaceFolder}/apps/common/config/include", "${workspaceFolder}/apps/soundbox/board/br23", "${workspaceFolder}/cpu/br23", "${workspaceFolder}/cpu/br23/audio_common", "${workspaceFolder}/cpu/br23/audio_dec", "${workspaceFolder}/cpu/br23/audio_enc", "${workspaceFolder}/cpu/br23/audio_effect", "${workspaceFolder}/cpu/br23/localtws", "${workspaceFolder}/include_lib/btstack", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/app/demo", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/app/product_test", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/app/uart_common", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/extern_components/mbedtls", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/port", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/sdk/include", "${workspaceFolder}/apps/common/third_party_profile/tuya_protocol/sdk/lib", "${workspaceFolder}/apps/common/usb", "${workspaceFolder}/apps/common/usb/device", "${workspaceFolder}/apps/common/usb/host", "${workspaceFolder}/apps/soundbox/smartbox", "${workspaceFolder}/apps/soundbox/smartbox/bt_manage/bt_trans_data", "${workspaceFolder}/apps/soundbox/smartbox/bt_manage", "${workspaceFolder}/apps/soundbox/smartbox/smartbox_setting", "${workspaceFolder}/apps/soundbox/smartbox/smartbox_setting/smartbox_misc_setting", "${workspaceFolder}/apps/soundbox/smartbox/smartbox_setting_opt", "${workspaceFolder}/apps/soundbox/smartbox/smartbox_update", "${workspaceFolder}/apps/soundbox/smartbox/file_transfer", "${workspaceFolder}/apps/soundbox/smartbox/tuya", "${workspaceFolder}/apps/soundbox/include/ui/color_led", "C:/JL/pi32/pi32v2-include"], "intelliSenseMode": "clang-x86", "name": "AC695N_soundbox"}], "version": 4}