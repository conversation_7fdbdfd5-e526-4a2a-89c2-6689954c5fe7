#ifndef _APP_STATUS_API_H
#define _APP_STATUS_API_H

#include "btstack/avctp_user.h"
enum APP_STATUS { //此枚举可自行添加状态
    APP_STATUS_POWER_ON,
    APP_STATUS_POWER_OFF,
    APP_STATUS_POWER_CLOSE,
    APP_STATUS_IDLE,
    APP_STATUS_BT_SECOND_CONNECTED,
    APP_STATUS_BT_FIRST_CONNECTED,
    APP_STATUS_BT_FIRST_DISCONNECT,
    APP_STATUS_BT_SECOND_DISCONNECT,
    APP_STATUS_BT_PHONE_INCOME,
    APP_STATUS_BT_PHONE_OUT,
    APP_STATUS_BT_PHONE_ACTIVE,
    APP_STATUS_BT_PHONE_HANGUP,
    APP_STATUS_BT_PHONE_NUMBER,
    APP_STATUS_BT_SCO_STATUS_CHANGE,
    APP_STATUS_BT_VOICE_RECOGNITION,
    APP_STATUS_BT_A2DP_MEDIA_START,
    APP_STATUS_BT_INIT_OK,
    APP_STATUS_BT_A2DP_MEDIA_STOP,
    APP_STATUS_MUSIC,
    APP_STATUS_MUSIC_QUIT,
    APP_STATUS_MUSIC_PLAY,
    APP_STATUS_MUSIC_PP,
    APP_STATUS_MUSIC_PN,
    APP_STATUS_MUSIC_FFR,
    APP_STATUS_PC,
    APP_STATUS_PC_COPY,
};


void app_status_handler(enum APP_STATUS status);
#endif
