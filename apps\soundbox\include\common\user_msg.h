#ifndef __USER_MSG_H__
#define __USER_MSG_H__


///用户自动消息定义, 注意此处是非按键消息

enum {
	USER_MSG_SYS_SMARTBOX_BEGIN = -0x1300,
	USER_MSG_SYS_SOUNDCARD_BEGIN = -0x1100,
	USER_MSG_SYS_SOUNDCARD_SLIDE_MIC,
	USER_MSG_SYS_SOUNDCARD_SLIDE_ECHO,
	USER_MSG_SYS_SOUNDCARD_SLIDE_TREBLE,
	USER_MSG_SYS_SOUNDCARD_SLIDE_BASS,
	USER_MSG_SYS_SOUNDCARD_SLIDE_REC_VOL,
	USER_MSG_SYS_SOUNDCARD_SLIDE_MUSIC_VOL,
	USER_MSG_SYS_SOUNDCARD_SLIDE_EAR_VOL,
	USER_MSG_SYS_SOUNDCARD_MIC1_STATUS,
	USER_MSG_SYS_SOUNDCARD_MIC2_STATUS,
	USER_MSG_SYS_SOUNDCARD_AUX0_STATUS,
	USER_MSG_SYS_SOUNDCARD_AUX_OPEN,

	USER_MSG_SYS_START = -0x1000,
    USER_MSG_SYS_MIXER_RECORD_SWITCH,
    USER_MSG_SYS_MIXER_RECORD_STOP,


    ///用户自定义消息从以下开始定义
    USER_MSG_TEST = 0x0,
};


#endif//__USER_MSG_H__

