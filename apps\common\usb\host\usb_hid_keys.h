/**
 * USB HID Keyboard scan codes as per USB spec 1.11
 * plus some additional codes
 *
 * Created by MightyPork, 2016
 * Public domain
 *
 * Adapted from:
 * https://source.android.com/devices/input/keyboard-devices.html
 */

#ifndef USB_HID_KEYS
#define USB_HID_KEYS

struct keyboard_data_t {
    u8 fun_key;
    u8 res;
    u8 Keypad[6];
} _GNU_PACKED_	;

struct mouse_data_t {
    u8 btn;
    s16 x;
    s16 y;
    s8 wheel;
    s8 ac_pan;
} _GNU_PACKED_	;
struct point_t {
    u8 tip_switch: 2;
    /* u8 in_range: 1; */
    u8 res: 2;
    u8 cid: 4;
    s16 x;
    s16 y;
} _GNU_PACKED_ ;

struct touch_screen_t {
    struct point_t p[3];
} _GNU_PACKED_;

struct mouse_point_t {
    u8 btn;
    s8 x;
    s8 y;
    s8 wheel;
} _GNU_PACKED_;
/**
 * Modifier masks - used for the first byte in the HID report.
 * NOTE: The second byte in the report is reserved, 0x00
 */
#define _KEY_MOD_LCTRL  0x01
#define _KEY_MOD_LSHIFT 0x02
#define _KEY_MOD_LALT   0x04
#define _KEY_MOD_LMETA  0x08
#define _KEY_MOD_RCTRL  0x10
#define _KEY_MOD_RSHIFT 0x20
#define _KEY_MOD_RALT   0x40
#define _KEY_MOD_RMETA  0x80

/**
 * Scan codes - last N slots in the HID report (usually 6).
 * 0x00 if no key pressed.
 *
 * If more than N keys are pressed, the HID reports
 * KEY_ERR_OVF in all slots to indicate this condition.
 */

#define _KEY_NONE 0x00 // No key pressed
#define _KEY_ERR_OVF 0x01 //  Keyboard Error Roll Over - used for all slots if too many keys are pressed ("Phantom key")
// 0x02 //  Keyboard POST Fail
// 0x03 //  Keyboard Error Undefined
#define _KEY_A 0x04 // Keyboard a and A
#define _KEY_B 0x05 // Keyboard b and B
#define _KEY_C 0x06 // Keyboard c and C
#define _KEY_D 0x07 // Keyboard d and D
#define _KEY_E 0x08 // Keyboard e and E
#define _KEY_F 0x09 // Keyboard f and F
#define _KEY_G 0x0a // Keyboard g and G
#define _KEY_H 0x0b // Keyboard h and H
#define _KEY_I 0x0c // Keyboard i and I
#define _KEY_J 0x0d // Keyboard j and J
#define _KEY_K 0x0e // Keyboard k and K
#define _KEY_L 0x0f // Keyboard l and L
#define _KEY_M 0x10 // Keyboard m and M
#define _KEY_N 0x11 // Keyboard n and N
#define _KEY_O 0x12 // Keyboard o and O
#define _KEY_P 0x13 // Keyboard p and P
#define _KEY_Q 0x14 // Keyboard q and Q
#define _KEY_R 0x15 // Keyboard r and R
#define _KEY_S 0x16 // Keyboard s and S
#define _KEY_T 0x17 // Keyboard t and T
#define _KEY_U 0x18 // Keyboard u and U
#define _KEY_V 0x19 // Keyboard v and V
#define _KEY_W 0x1a // Keyboard w and W
#define _KEY_X 0x1b // Keyboard x and X
#define _KEY_Y 0x1c // Keyboard y and Y
#define _KEY_Z 0x1d // Keyboard z and Z

#define _KEY_1 0x1e // Keyboard 1 and !
#define _KEY_2 0x1f // Keyboard 2 and @
#define _KEY_3 0x20 // Keyboard 3 and #
#define _KEY_4 0x21 // Keyboard 4 and $
#define _KEY_5 0x22 // Keyboard 5 and %
#define _KEY_6 0x23 // Keyboard 6 and ^
#define _KEY_7 0x24 // Keyboard 7 and &
#define _KEY_8 0x25 // Keyboard 8 and *
#define _KEY_9 0x26 // Keyboard 9 and (
#define _KEY_0 0x27 // Keyboard 0 and )

#define _KEY_ENTER 0x28 // Keyboard Return (ENTER)
#define _KEY_ESC 0x29 // Keyboard ESCAPE
#define _KEY_BACKSPACE 0x2a // Keyboard DELETE (Backspace)
#define _KEY_TAB 0x2b // Keyboard Tab
#define _KEY_SPACE 0x2c // Keyboard Spacebar
#define _KEY_MINUS 0x2d // Keyboard - and _
#define _KEY_EQUAL 0x2e // Keyboard = and +
#define _KEY_LEFTBRACE 0x2f // Keyboard [ and {
#define _KEY_RIGHTBRACE 0x30 // Keyboard ] and }
#define _KEY_BACKSLASH 0x31 // Keyboard \ and |
#define _KEY_HASHTILDE 0x32 // Keyboard Non-US # and ~
#define _KEY_SEMICOLON 0x33 // Keyboard ; and :
#define _KEY_APOSTROPHE 0x34 // Keyboard ' and "
#define _KEY_GRAVE 0x35 // Keyboard ` and ~
#define _KEY_COMMA 0x36 // Keyboard , and <
#define _KEY_DOT 0x37 // Keyboard . and >
#define _KEY_SLASH 0x38 // Keyboard / and ?
#define _KEY_CAPSLOCK 0x39 // Keyboard Caps Lock

#define _KEY_F1 0x3a // Keyboard F1
#define _KEY_F2 0x3b // Keyboard F2
#define _KEY_F3 0x3c // Keyboard F3
#define _KEY_F4 0x3d // Keyboard F4
#define _KEY_F5 0x3e // Keyboard F5
#define _KEY_F6 0x3f // Keyboard F6
#define _KEY_F7 0x40 // Keyboard F7
#define _KEY_F8 0x41 // Keyboard F8
#define _KEY_F9 0x42 // Keyboard F9
#define _KEY_F10 0x43 // Keyboard F10
#define _KEY_F11 0x44 // Keyboard F11
#define _KEY_F12 0x45 // Keyboard F12

#define _KEY_SYSRQ 0x46 // Keyboard Print Screen
#define _KEY_SCROLLLOCK 0x47 // Keyboard Scroll Lock
#define _KEY_PAUSE 0x48 // Keyboard Pause
#define _KEY_INSERT 0x49 // Keyboard Insert
#define _KEY_HOME 0x4a // Keyboard Home
#define _KEY_PAGEUP 0x4b // Keyboard Page Up
#define _KEY_DELETE 0x4c // Keyboard Delete Forward
#define _KEY_END 0x4d // Keyboard End
#define _KEY_PAGEDOWN 0x4e // Keyboard Page Down
#define _KEY_RIGHT 0x4f // Keyboard Right Arrow
#define _KEY_LEFT 0x50 // Keyboard Left Arrow
#define _KEY_DOWN 0x51 // Keyboard Down Arrow
#define _KEY_UP 0x52 // Keyboard Up Arrow

#define _KEY_NUMLOCK 0x53 // Keyboard Num Lock and Clear
#define _KEY_KPSLASH 0x54 // Keypad /
#define _KEY_KPASTERISK 0x55 // Keypad *
#define _KEY_KPMINUS 0x56 // Keypad -
#define _KEY_KPPLUS 0x57 // Keypad +
#define _KEY_KPENTER 0x58 // Keypad ENTER
#define _KEY_KP1 0x59 // Keypad 1 and End
#define _KEY_KP2 0x5a // Keypad 2 and Down Arrow
#define _KEY_KP3 0x5b // Keypad 3 and PageDn
#define _KEY_KP4 0x5c // Keypad 4 and Left Arrow
#define _KEY_KP5 0x5d // Keypad 5
#define _KEY_KP6 0x5e // Keypad 6 and Right Arrow
#define _KEY_KP7 0x5f // Keypad 7 and Home
#define _KEY_KP8 0x60 // Keypad 8 and Up Arrow
#define _KEY_KP9 0x61 // Keypad 9 and Page Up
#define _KEY_KP0 0x62 // Keypad 0 and Insert
#define _KEY_KPDOT 0x63 // Keypad . and Delete

#define _KEY_102ND 0x64 // Keyboard Non-US \ and |
#define _KEY_COMPOSE 0x65 // Keyboard Application
#define _KEY_POWER 0x66 // Keyboard Power
#define _KEY_KPEQUAL 0x67 // Keypad =

#define _KEY_F13 0x68 // Keyboard F13
#define _KEY_F14 0x69 // Keyboard F14
#define _KEY_F15 0x6a // Keyboard F15
#define _KEY_F16 0x6b // Keyboard F16
#define _KEY_F17 0x6c // Keyboard F17
#define _KEY_F18 0x6d // Keyboard F18
#define _KEY_F19 0x6e // Keyboard F19
#define _KEY_F20 0x6f // Keyboard F20
#define _KEY_F21 0x70 // Keyboard F21
#define _KEY_F22 0x71 // Keyboard F22
#define _KEY_F23 0x72 // Keyboard F23
#define _KEY_F24 0x73 // Keyboard F24

#define _KEY_OPEN 0x74 // Keyboard Execute
#define _KEY_HELP 0x75 // Keyboard Help
#define _KEY_PROPS 0x76 // Keyboard Menu
#define _KEY_FRONT 0x77 // Keyboard Select
#define _KEY_STOP 0x78 // Keyboard Stop
#define _KEY_AGAIN 0x79 // Keyboard Again
#define _KEY_UNDO 0x7a // Keyboard Undo
#define _KEY_CUT 0x7b // Keyboard Cut
#define _KEY_COPY 0x7c // Keyboard Copy
#define _KEY_PASTE 0x7d // Keyboard Paste
#define _KEY_FIND 0x7e // Keyboard Find
#define _KEY_MUTE 0x7f // Keyboard Mute
#define _KEY_VOLUMEUP 0x80 // Keyboard Volume Up
#define _KEY_VOLUMEDOWN 0x81 // Keyboard Volume Down
// 0x82  Keyboard Locking Caps Lock
// 0x83  Keyboard Locking Num Lock
// 0x84  Keyboard Locking Scroll Lock
#define _KEY_KPCOMMA 0x85 // Keypad Comma
// 0x86  Keypad Equal Sign
#define _KEY_RO 0x87 // Keyboard International1
#define _KEY_KATAKANAHIRAGANA 0x88 // Keyboard International2
#define _KEY_YEN 0x89 // Keyboard International3
#define _KEY_HENKAN 0x8a // Keyboard International4
#define _KEY_MUHENKAN 0x8b // Keyboard International5
#define _KEY_KPJPCOMMA 0x8c // Keyboard International6
// 0x8d  Keyboard International7
// 0x8e  Keyboard International8
// 0x8f  Keyboard International9
#define _KEY_HANGEUL 0x90 // Keyboard LANG1
#define _KEY_HANJA 0x91 // Keyboard LANG2
#define _KEY_KATAKANA 0x92 // Keyboard LANG3
#define _KEY_HIRAGANA 0x93 // Keyboard LANG4
#define _KEY_ZENKAKUHANKAKU 0x94 // Keyboard LANG5
// 0x95  Keyboard LANG6
// 0x96  Keyboard LANG7
// 0x97  Keyboard LANG8
// 0x98  Keyboard LANG9
// 0x99  Keyboard Alternate Erase
// 0x9a  Keyboard SysReq/Attention
// 0x9b  Keyboard Cancel
// 0x9c  Keyboard Clear
// 0x9d  Keyboard Prior
// 0x9e  Keyboard Return
// 0x9f  Keyboard Separator
// 0xa0  Keyboard Out
// 0xa1  Keyboard Oper
// 0xa2  Keyboard Clear/Again
// 0xa3  Keyboard CrSel/Props
// 0xa4  Keyboard ExSel

// 0xb0  Keypad 00
// 0xb1  Keypad 000
// 0xb2  Thousands Separator
// 0xb3  Decimal Separator
// 0xb4  Currency Unit
// 0xb5  Currency Sub-unit
#define _KEY_KPLEFTPAREN 0xb6 // Keypad (
#define _KEY_KPRIGHTPAREN 0xb7 // Keypad )
// 0xb8  Keypad {
// 0xb9  Keypad }
// 0xba  Keypad Tab
// 0xbb  Keypad Backspace
// 0xbc  Keypad A
// 0xbd  Keypad B
// 0xbe  Keypad C
// 0xbf  Keypad D
// 0xc0  Keypad E
// 0xc1  Keypad F
// 0xc2  Keypad XOR
// 0xc3  Keypad ^
// 0xc4  Keypad %
// 0xc5  Keypad <
// 0xc6  Keypad >
// 0xc7  Keypad &
// 0xc8  Keypad &&
// 0xc9  Keypad |
// 0xca  Keypad ||
// 0xcb  Keypad :
// 0xcc  Keypad #
// 0xcd  Keypad Space
// 0xce  Keypad @
// 0xcf  Keypad !
// 0xd0  Keypad Memory Store
// 0xd1  Keypad Memory Recall
// 0xd2  Keypad Memory Clear
// 0xd3  Keypad Memory Add
// 0xd4  Keypad Memory Subtract
// 0xd5  Keypad Memory Multiply
// 0xd6  Keypad Memory Divide
// 0xd7  Keypad +/-
// 0xd8  Keypad Clear
// 0xd9  Keypad Clear Entry
// 0xda  Keypad Binary
// 0xdb  Keypad Octal
// 0xdc  Keypad Decimal
// 0xdd  Keypad Hexadecimal

#define _KEY_LEFTCTRL 0xe0 // Keyboard Left Control
#define _KEY_LEFTSHIFT 0xe1 // Keyboard Left Shift
#define _KEY_LEFTALT 0xe2 // Keyboard Left Alt
#define _KEY_LEFTMETA 0xe3 // Keyboard Left GUI
#define _KEY_RIGHTCTRL 0xe4 // Keyboard Right Control
#define _KEY_RIGHTSHIFT 0xe5 // Keyboard Right Shift
#define _KEY_RIGHTALT 0xe6 // Keyboard Right Alt
#define _KEY_RIGHTMETA 0xe7 // Keyboard Right GUI

#define _KEY_MEDIA_PLAYPAUSE 0xe8
#define _KEY_MEDIA_STOPCD 0xe9
#define _KEY_MEDIA_PREVIOUSSONG 0xea
#define _KEY_MEDIA_NEXTSONG 0xeb
#define _KEY_MEDIA_EJECTCD 0xec
#define _KEY_MEDIA_VOLUMEUP 0xed
#define _KEY_MEDIA_VOLUMEDOWN 0xee
#define _KEY_MEDIA_MUTE 0xef
#define _KEY_MEDIA_WWW 0xf0
#define _KEY_MEDIA_BACK 0xf1
#define _KEY_MEDIA_FORWARD 0xf2
#define _KEY_MEDIA_STOP 0xf3
#define _KEY_MEDIA_FIND 0xf4
#define _KEY_MEDIA_SCROLLUP 0xf5
#define _KEY_MEDIA_SCROLLDOWN 0xf6
#define _KEY_MEDIA_EDIT 0xf7
#define _KEY_MEDIA_SLEEP 0xf8
#define _KEY_MEDIA_COFFEE 0xf9
#define _KEY_MEDIA_REFRESH 0xfa
#define _KEY_MEDIA_CALC 0xfb

#define _KEY_CUSTOM_CTRL_VOL_UP         0x00e9
#define _KEY_CUSTOM_CTRL_VOL_DOWN       0x00ea
#define _KEY_CUSTOM_CTRL_MUTE           0x00e2
#define _KEY_CUSTOM_CTRL_FORWARD        0x00b5
#define _KEY_CUSTOM_CTRL_STOP           0x00cd
#define _KEY_CUSTOM_CTRL_BACK           0x00b6
#define _KEY_CUSTOM_CTRL_MUSIC          0x0183
#define _KEY_CUSTOM_CTRL_CALCULATOR     0x0192
#define _KEY_CUSTOM_CTRL_SEARCH         0x0221
#define _KEY_CUSTOM_CTRL_EMAIL          0x018A
#define _KEY_CUSTOM_CTRL_HOME           0x0223
#define _KEY_BRIGHTNESS_INCREASE        0x006F
#define _KEY_BRIGHTNESS_REDUCTION       0x0070
#define _KEY_ZOOM_IN                    0x022D
#define _KEY_ZOOM_OUT                   0x022E
#define _KEY_CUSTOM_COPY                0x021B
#define _KEY_CUSTOM_CUT                 0x021C
#define _KEY_CUSTOM_PASTE               0x021D
#define _KEY_CUSTOM_SELECT_ALL          0x021E
#define _KEY_CUSTOM_SPLIT_SCREEN        0x0196
#define _KEY_CUSTOM_LOCK                0x0030
#define _KEY_CUSTOM_ESC                 0x0223
#define _KEY_CUSTOM_CALENDAR            0x018e
#define _KEY_CUSTOM_MESSAGE             0X018d

#define _KEY_CUSTOM_BROWER              0x0231
#define _KEY_CUSTOM_PROCESS             0x023a

#define _KEY_CUSTOM_SET                 0x029b

#define LED_NUM_LOCK     0x01
#define LED_CAPS_LOCK    0x02
#define LED_SCROLL_CLOCK 0x04
#endif

