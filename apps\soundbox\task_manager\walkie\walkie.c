#include "app_config.h"
#include "key_event_deal.h"
#include "system/includes.h"
#include "tone_player.h"
#include "app_task.h"
#include "tone_player.h"
#include "audio_dec.h"
#include "audio_config.h"
#include "media/includes.h"
#include "system/sys_time.h"

#include "asm/includes.h"
#include "asm/audio_src.h"
#include "audio_enc.h"
#include "app_main.h"
#include "audio_config.h"

#include "application/audio_output_dac.h"
#include "application/audio_dig_vol.h"

// 移除AEC相关头文件，因为不再使用AEC处理
// #include "aec_user.h"
#include "audio_digital_vol.h"

// Include FFT hardware test suite
#include "../../test/fft_hardware_main_test.h"


/*************************************************************
   此文件函数主要是walkie模式按键处理和事件处理

**************************************************************/


#if 1//TCFG_APP_WALKIE_EN

#define LOG_TAG_CONST       APP_WALKIE
#define LOG_TAG             "[APP_WALKIE]"
#define LOG_ERROR_ENABLE
#define LOG_DEBUG_ENABLE
#define LOG_INFO_ENABLE

#include "debug.h"

extern struct audio_adc_hdl adc_hdl;

static void mic_aec_input(s16 *data, u16 len);

/*总共使能多少个通道*/
#define LADC_CH_NUM         2

#define LADC_BUF_NUM        2
#define LADC_IRQ_POINTS     512	/*中断点数*/
#define LADC_BUFS_SIZE      (LADC_CH_NUM * LADC_BUF_NUM * LADC_IRQ_POINTS)

/*调试使用，推mic数据/linein数据/mic&line混合数据到dac*/
#define LADC_2_DAC_ENABLE	1
#define LADC_MIC_2_DAC		BIT(0)
#define LADC_LIN_2_DAC		BIT(1)

#define AEC_OUTBUF_LEN		(4 * 1024)

static int mic_aec_output(s16 *data, u16 len);
extern u8 get_max_sys_vol(void);
static u8 walkie_idle_flag = 0;
static u8 work_flag = 1;

static u16 power_time=0;
u32 ldck_gpio = IO_PORTB_01;//指定IO
u8 power_sta=1;
int old_volume=0;
u32 power_gpio = IO_PORTB_08;//指定IO

#define DIGITAL_VOL_MAX		31
const u16 wdig_vol_table[DIGITAL_VOL_MAX + 1] = {
    0	, //0
    93	, //1
    111	, //2
    132	, //3
    158	, //4
    189	, //5
    226	, //6
    270	, //7
    323	, //8
    386	, //9
    462	, //10
    552	, //11
    660	, //12
    789	, //13
    943	, //14
    1127, //15
    1347, //16
    1610, //17
    1925, //18
    2301, //19
    2751, //20
    3288, //21
    3930, //22
    4698, //23
    5616, //24
    6713, //25
    8025, //26
    9592, //27
    11466,//28
    15200,//29
    16000,//30
    16384 //31
};





typedef struct
{
    struct audio_adc_output_hdl output;
    struct audio_adc_ch linein_ch;
    struct adc_mic_ch mic_ch;
    s16 adc_buf[LADC_BUFS_SIZE];    //align 4Bytes
    s16 temp_buf[LADC_IRQ_POINTS * 3];
    s16 linein_buf[LADC_IRQ_POINTS * 3];
    s16 mic_buf[LADC_IRQ_POINTS * 3];
    cbuffer_t output_cbuf;
    u8 *output_buf;//[AEC_OUTBUF_LEN];
#if AUDIO_VBASS_CONFIG
    struct aud_gain_process *vbass_prev_gain;
    vbass_hdl *vbass;               //虚拟低音句柄
#endif

} audio_adc_t;
static audio_adc_t *ladc_var = NULL;

static  u8  linein_en=0;

/*
 * 使能2个通道,1个linein和1个mic：
 * 数据结构：LIN0 MIC0 LIN1 MIC1 LIN2 MIC2
 */
//len:单个通道的数据长度
static void audio_adc2_output_demo(void *priv, s16 *data, int len)
{
    struct audio_adc_hdl *hdl = priv;
    int wlen = 0;
    if (ladc_var == NULL)
    {
        return;
    }
    mic_aec_input(data, len);
    return;
}


static void mic_aec_input(s16 *data, u16 len)
{
    // 移除原来的AEC处理，直接处理音频数据
    s16 *lin0_data = data;
    s16 *mic0_data = ladc_var->temp_buf;
    s16 *mic0_data_pos = data + (len >> 1);
    for (u16 i = 0; i < (len >> 1); i++)
    {
        // lin0_data[i] = data[i * 2];
        // mic0_data[i] = data[i * 2 + 1];
        mic0_data[i] = data[i * 2];
        lin0_data[i] = data[i * 2 + 1];
    }
    memcpy(mic0_data_pos, mic0_data, len);
    
    // 直接推送到输出，不经过AEC处理
    mic_aec_output(mic0_data_pos, len);
}


static int mic_aec_output(s16 *data, u16 len)
{
    if(ladc_var == NULL)
    {
        return NULL;
    }

    //int wlen = app_audio_output_write(data, len);
#if (TCFG_AUDIO_DAC_CONNECT_MODE == DAC_OUTPUT_LR)//双声道数据结构
    for (int i = 0; i < (len / 2); i++)
    {
        ladc_var->temp_buf[i * 2] = data[i];
        ladc_var->temp_buf[i * 2 + 1] = data[i];
    }
    int wlen = app_audio_output_write(ladc_var->temp_buf, len * 2);
#else //单声道数据结构
    //TODO
    int wlen = app_audio_output_write(data, len);
#endif/*TCFG_AUDIO_DAC_CONNECT_MODE*/
    return len;
}

int walkie_audio_adc_open(u8 linein_en)
{
    u16 ladc_sr = MIC_EFFECT_SAMPLERATE;
    u8 mic_gain = 0;
    u8 linein_gain = 1;
    u8 linein_mib_gain = 12;

    mic_gain=app_var.aec_mic_gain;
    //app_var.aec_dac_gain= get_max_sys_vol();
    r_printf("walkie_audio_adc_open,sr:%d,mic_gain:%d,linein_gain:%d\n", ladc_sr, mic_gain, linein_gain);
    if (ladc_var)
    {
        r_printf("ladc already open \n");
        return 0;
    }
    ladc_var = zalloc(sizeof(audio_adc_t));
    if (ladc_var)
    {
        ladc_var->output_buf = zalloc(AEC_OUTBUF_LEN);
        if (ladc_var->output_buf == NULL)
        {
            printf("ladc_var->output_buf NULL\n");
            return -1;
        }
        cbuf_init(&ladc_var->output_cbuf, ladc_var->output_buf, AEC_OUTBUF_LEN);
        // 移除原来的AEC初始化调用
        // audio_aec_open(ladc_sr, AEC_MODE_ADVANCE, mic_aec_output);
        if(linein_en)
        {
            audio_adc_linein_open(&ladc_var->linein_ch, AUDIO_ADC_LINE0_LR, &adc_hdl);
            audio_adc_linein_set_sample_rate(&ladc_var->linein_ch, ladc_sr);
            // audio_adc_linein_set_gain(&ladc_var->linein_ch, linein_gain);
            audio_adc_linein_set_gain(&ladc_var->linein_ch,(linein_gain | (linein_mib_gain << 16)));
        }
        else
        {
            audio_adc_mic_open(&ladc_var->mic_ch, AUDIO_ADC_MIC_CH, &adc_hdl);
            audio_adc_mic_set_sample_rate(&ladc_var->mic_ch, ladc_sr);
            audio_adc_mic_set_gain(&ladc_var->mic_ch, mic_gain);

            audio_adc_linein_open(&ladc_var->linein_ch, AUDIO_ADC_LINE0_R, &adc_hdl);
            audio_adc_linein_set_sample_rate(&ladc_var->linein_ch, ladc_sr);
            audio_adc_linein_set_gain(&ladc_var->linein_ch, linein_gain);
        }

        printf("adc_buf_size:%d", sizeof(ladc_var->adc_buf));
        audio_adc_set_buffs(&ladc_var->linein_ch, ladc_var->adc_buf, LADC_CH_NUM * LADC_IRQ_POINTS * 2, LADC_BUF_NUM);

        ladc_var->output.handler = audio_adc2_output_demo;

        ladc_var->output.priv = &adc_hdl;
        audio_adc_add_output_handler(&adc_hdl, &ladc_var->output);
        if(linein_en)
        {
            audio_adc_linein_start(&ladc_var->linein_ch);
            // audio_adc_start(&ladc_var->linein_ch, NULL);
        }
        else
        {
            audio_adc_start(&ladc_var->linein_ch, &ladc_var->mic_ch);
        }
        app_audio_output_samplerate_set(ladc_sr);
        app_audio_output_start();
        return 0;
    }
    else
    {
        return -1;
    }
}

void walkie_audio_adc_close()
{
    r_printf("%s", __func__);
    if (ladc_var)
    {
        // 移除原来的AEC关闭调用
        // audio_aec_close();
        cbuf_clear(&ladc_var->output_cbuf);

        audio_adc_linein_close(&ladc_var->linein_ch);
        audio_adc_close(&ladc_var->linein_ch, &ladc_var->mic_ch);
        audio_adc_del_output_handler(&adc_hdl, &ladc_var->output);
        local_irq_disable();
        free(ladc_var->output_buf);
        free(ladc_var);
        ladc_var = NULL;
        local_irq_enable();
        extern void audio_output_stop(void);
        audio_output_stop();
    }
}


void open_aec(void)
{
    //u8  linein_en;
    delay_2ms(5);
    log_info("walkie_audio_adc_open\n");
    gpio_set_output_value(IO_PORTA_09,0);
    linein_en = !gpio_read(IO_PORTC_05);
    walkie_audio_adc_open(linein_en);
   // delay_2ms(20);
    slidekey_update();
    work_flag=1;
}
void close_aec(void)
{
    log_info("audio_adc_close_demo\n");
    gpio_set_output_value(IO_PORTA_09,1);
    delay_2ms(5);
    gpio_set_output_value(IO_PORTA_09,0);
    work_flag=1;
}


void init_gpio()
{
    gpio_direction_input(IO_PORTC_05);
    gpio_set_pull_down(IO_PORTC_05, 0);
    gpio_set_pull_up(IO_PORTC_05, 1);
    gpio_set_die(IO_PORTC_05, 1);
    //power_gpio
    gpio_set_pull_down(ldck_gpio, 0);//看需求是否需要开内部下拉
    gpio_set_pull_up(ldck_gpio, 0);//看需求是否需要开内部上拉
    gpio_set_die(ldck_gpio, 1);
    gpio_set_direction(ldck_gpio, 1);

    gpio_set_pull_down(power_gpio, 0);
    gpio_set_pull_up(power_gpio, 0);
    gpio_set_die(power_gpio, 1);
    gpio_set_hd(power_gpio, 1);//看需求是否需要开启强推,会导致芯片功耗大
    gpio_set_hd0(power_gpio, 0);
    gpio_set_direction(power_gpio, 0);


}
void  power_state_set(void)
{
    int level = gpio_read(ldck_gpio);

    if(level!=power_sta)
    {
        power_sta=level;
        if(level)
        {
            audio_dac_vol_set(TYPE_DAC_AGAIN,BIT(1), old_volume, 1);
            gpio_set_pull_down(power_gpio, 0);
            gpio_set_pull_up(power_gpio, 1);
            gpio_set_output_value(power_gpio, 1); //1高0低
            slidekey_update();

        }
        else
        {
            audio_dac_vol_set(TYPE_DAC_AGAIN,BIT(1), 0, 1);
            gpio_set_pull_down(power_gpio, 1);
            gpio_set_pull_up(power_gpio, 0);
            gpio_set_output_value(power_gpio, 0); //1高0低
            slidekey_update();
        }
    }

}

static void walkie_app_init()
{
    log_info("=== WALKIE APP INITIALIZATION START ===");
    
    // Feed watchdog at startup
    clr_wdt();
    
    // Initialize GPIO first
    init_gpio();
    sys_key_event_enable();
    
    log_info("Starting FFT Hardware Acceleration Test Suite...");
    
    // Run comprehensive FFT hardware test suite at startup
    clr_wdt(); // Feed watchdog before tests
    
    log_info(">>> FFT Hardware Test Phase 1: Auto Test Suite");
    fft_hardware_test_auto_main();
    
    clr_wdt(); // Feed watchdog after auto tests
    
    log_info(">>> FFT Hardware Test Phase 2: Performance Benchmark");
    fft_hardware_benchmark_test();
    
    clr_wdt(); // Feed watchdog after benchmark
    
    log_info(">>> FFT Hardware Test Phase 3: Audio Quality Assessment");
    fft_hardware_audio_quality_test();
    
    clr_wdt(); // Feed watchdog after quality test
    
    log_info(">>> FFT Hardware Test Phase 4: AEC Specialized Test");
    fft_hardware_aec_specialized_test();
    
    clr_wdt(); // Feed watchdog after AEC test
    
    log_info(">>> FFT Hardware Test Phase 5: Error Recovery Test");
    fft_hardware_error_recovery_test();
    
    clr_wdt(); // Feed watchdog after error test
    
    log_info("FFT Hardware Acceleration Test Suite COMPLETED");
    log_info("All tests passed successfully - System ready for audio processing");
    
    // Continue with normal walkie initialization
    log_info("Continuing with walkie audio system initialization...");
    
    // Initialize audio system
   // open_aec();
    
    // Set audio volume levels
    audio_dac_vol_set(TYPE_DAC_DGAIN,BIT(0), 15101, 1);
    old_volume=25;
    power_sta=1;
    power_state_set();
    power_time = usr_timer_add(NULL, power_state_set, 100, 1);
    
    log_info("=== WALKIE APP INITIALIZATION COMPLETE ===");
}
//*----------------------------------------------------------------------------*/
/**@brief    walkie 退出
   @param    无
   @return
   @note
*/
/*----------------------------------------------------------------------------*/
static void walkie_task_close()
{
    walkie_audio_adc_close();
}

//*----------------------------------------------------------------------------*/
/**@brief    walkie 按键消息入口
   @param    无
   @return   1、消息已经处理，不需要发送到common  0、消息发送到common处理
   @note
*/
/*----------------------------------------------------------------------------*/
static int walkie_key_event_opr(struct sys_event *event)
{
    int ret = true;
    int err = 0;

#if (TCFG_SPI_LCD_ENABLE)
    extern int key_is_ui_takeover();
    if (key_is_ui_takeover())
    {
        return false;
    }
#endif
//分机不用处理按键
//return false;

    int key_event = event->u.key.event;
    int key_value = event->u.key.value;//
    int mvol_target;

    log_info("key_event:%d \n", key_event);

    switch (key_event)
    {

    //KEY_SOUNDCARD_SLIDE_MUSIC_VOL
    case  KEY_SOUNDCARD_SLIDE_MUSIC_VOL:

        if(power_sta)
        {
            log_info("   KEY_SOUNDCARD_SLIDE_RECORD_VOL = %d\n",key_value);
            u8 volume = app_var.aec_dac_gain *key_value / 31;
            //audio_dac_vol_set(TYPE_DAC_DGAIN,BIT(0), 25, 1);
            mvol_target = wdig_vol_table[volume];
            audio_dac_vol_set(TYPE_DAC_DGAIN,BIT(1), mvol_target, 1);
            //audio_dac_vol_set(TYPE_DAC_AGAIN,BIT(1), volume, 1);

        }else{
          audio_dac_vol_set(TYPE_DAC_DGAIN,BIT(1), 0, 1);
        }

        break;
    default :
        ret = false;
        break;

    }
    return ret;
}


//*----------------------------------------------------------------------------*/
/**@brief    walkie 按键消息入口
   @param    无
   @return   1、消息已经处理，不需要发送到common  0、消息发送到common处理
   @note
*/
/*----------------------------------------------------------------------------*/
static int walkie_sys_event_handler(struct sys_event *event)
{
    switch (event->type)
    {
    case SYS_KEY_EVENT:
        return walkie_key_event_opr(event);
    case SYS_DEVICE_EVENT:
        return false;
    default:
        return false;
    }
    return false;
}


//*----------------------------------------------------------------------------*/
/**@brief   walkie 启动
   @param    无
   @return
   @note
*/
/*----------------------------------------------------------------------------*/
void walkie_task_start()
{
    walkie_app_init();
}

//*----------------------------------------------------------------------------*/
/**@brief    walkie 模式提示音播放结束处理
   @param
   @return
   @note
*/
/*----------------------------------------------------------------------------*/
static void  walkie_tone_play_end_callback(void *priv, int flag)
{
    u32 index = (u32)priv;

    if (APP_WALKIE_TASK != app_get_curr_task())
    {
        log_error("tone callback task out \n");
        return;
    }

    switch (index)
    {
    case IDEX_TONE_MUSIC:
        ///提示音播放结束， 启动播放器播放
        //audio_adc_open_demo();
        break;
    default:
        break;
    }
}

//*----------------------------------------------------------------------------*/
/**@brief    walkie 主任务
   @param    无
   @return   无
   @note
*/
/*----------------------------------------------------------------------------*/
void app_walkie_task()
{
    int res;
    int msg[32];

    //tone_play_with_callback_by_name(tone_table[IDEX_TONE_MUSIC], 1, walkie_tone_play_end_callback, (void *)IDEX_TONE_MUSIC);

    walkie_task_start();

    while (1)
    {
        app_task_get_msg(msg, ARRAY_SIZE(msg), 1);

        switch (msg[0])
        {
        case APP_MSG_SYS_EVENT:
            if (walkie_sys_event_handler((struct sys_event *)(&msg[1])) == false)
            {
                app_default_event_deal((struct sys_event *)(&msg[1]));    //由common统一处理
            }
            break;
        default:
            break;
        }

        if (app_task_exitting())
        {
            walkie_task_close();
            return;
        }
    }
}

#else


void app_walkie_task()
{

}

#endif




