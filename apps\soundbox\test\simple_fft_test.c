/*
 * Simple FFT Test Program
 * Minimal version to test basic FFT functionality without complex logging
 */

#include "system/includes.h"
#include "hw_fft.h"
#include <string.h>
#include <stdlib.h>

// Simple logging using printf
#define TEST_LOG(format, ...)  printf("[FFT] " format "\r\n", ## __VA_ARGS__)

// Real FFT buffer sizes
#define FFT_256_SIZE        256
#define FFT_256_BUF_SIZE    258  // (256/2+1)*2 = 258
#define FFT_512_SIZE        512
#define FFT_512_BUF_SIZE    514  // (512/2+1)*2 = 514

// Test data generation
static void generate_test_data(int *data, int size, int type) {
    switch (type) {
        case 0: // Zero data
            memset(data, 0, size * sizeof(int));
            break;
        case 1: // Sine wave
            for (int i = 0; i < size; i++) {
                int angle = (i * 360) / size;
                if (angle < 90) {
                    data[i] = (angle * 8191) / 90;
                } else if (angle < 180) {
                    data[i] = ((180 - angle) * 8191) / 90;
                } else if (angle < 270) {
                    data[i] = -((angle - 180) * 8191) / 90;
                } else {
                    data[i] = -((360 - angle) * 8191) / 90;
                }
            }
            break;
        case 2: // Random data
            for (int i = 0; i < size; i++) {
                data[i] = (rand() % 16384) - 8192;
            }
            break;
        default: // Ramp
            for (int i = 0; i < size; i++) {
                data[i] = (i - size/2) / 8;
            }
            break;
    }
}

// Simple FFT test
static int test_fft(int fft_size, int fft_log2, int buf_size, const char *name) {
    int *test_buf = malloc(buf_size * sizeof(int));
    if (!test_buf) {
        TEST_LOG("Memory allocation failed for %s", name);
        return 0;
    }
    
    TEST_LOG("Testing %s (N=%d, Buffer=%d)", name, fft_size, buf_size);
    
    // Generate test data
    generate_test_data(test_buf, fft_size, 1); // Sine wave
    
    // Configure FFT
    unsigned int fft_cfg = hw_fft_config(fft_size, fft_log2, 1, 1, 0);
    
    // Execute FFT
    unsigned int start_time = timer_get_ms();
    hw_fft_run(fft_cfg, test_buf, test_buf);
    unsigned int end_time = timer_get_ms();
    
    TEST_LOG("FFT completed in %u ms", end_time - start_time);
    
    // Test IFFT
    unsigned int ifft_cfg = hw_fft_config(fft_size, fft_log2, 1, 1, 1);
    
    start_time = timer_get_ms();
    hw_fft_run(ifft_cfg, test_buf, test_buf);
    end_time = timer_get_ms();
    
    TEST_LOG("IFFT completed in %u ms", end_time - start_time);
    TEST_LOG("Test PASSED");
    
    free(test_buf);
    return 1;
}

// Performance test
static void performance_test(void) {
    TEST_LOG("=== Performance Test ===");
    
    const int iterations = 10;
    int test_buf[FFT_256_BUF_SIZE];
    
    generate_test_data(test_buf, FFT_256_SIZE, 2); // Random data
    
    unsigned int fft_cfg = hw_fft_config(FFT_256_SIZE, 8, 1, 1, 0);
    
    unsigned int total_time = timer_get_ms();
    for (int i = 0; i < iterations; i++) {
        hw_fft_run(fft_cfg, test_buf, test_buf);
    }
    total_time = timer_get_ms() - total_time;
    
    TEST_LOG("256-point FFT: %u ms total, %u ms avg (%d iterations)", 
             total_time, total_time / iterations, iterations);
}

// Main test function
void fft_test_main(void) {
    srand(timer_get_ms());
    
    TEST_LOG("=== Real FFT Hardware Test ===");
    TEST_LOG("Buffer Size Guide:");
    TEST_LOG("  256-point: int tmpbuf[%d];", FFT_256_BUF_SIZE);
    TEST_LOG("  512-point: int tmpbuf[%d];", FFT_512_BUF_SIZE);
    TEST_LOG("Formula: (N/2+1)*2 for N-point real FFT");
    TEST_LOG("");
    
    int passed = 0;
    int total = 0;
    
    // Test 256-point FFT
    total++;
    if (test_fft(FFT_256_SIZE, 8, FFT_256_BUF_SIZE, "256-point Real FFT")) {
        passed++;
    }
    
    // Test 512-point FFT
    total++;
    if (test_fft(FFT_512_SIZE, 9, FFT_512_BUF_SIZE, "512-point Real FFT")) {
        passed++;
    }
    
    // Performance test
    performance_test();
    
    // Final report
    TEST_LOG("");
    TEST_LOG("=== Test Summary ===");
    TEST_LOG("Total: %d, Passed: %d, Failed: %d", total, passed, total - passed);
    TEST_LOG("Success Rate: %d%%", (passed * 100) / total);
    
    if (passed == total) {
        TEST_LOG("SUCCESS: All FFT tests passed!");
    } else {
        TEST_LOG("WARNING: Some tests failed!");
    }
    
    TEST_LOG("");
    TEST_LOG("=== Buffer Size Reminder ===");
    TEST_LOG("256-point real FFT: int tmpbuf[258];");
    TEST_LOG("512-point real FFT: int tmpbuf[514];");
    TEST_LOG("General formula: int tmpbuf[(N/2+1)*2];");
    TEST_LOG("=== Test Complete ===");
}
