objs/apps/soundbox/smartbox/browser/browser.c.o: \
  apps/soundbox/smartbox/browser/browser.c \
  apps/soundbox/smartbox\browser/browser.h \
  include_lib/system/generic\typedef.h \
  include_lib/driver/cpu/br23\asm/cpu.h \
  include_lib/driver/cpu/br23\asm/br23.h \
  include_lib/driver/cpu/br23\asm/csfr.h \
  include_lib/driver/cpu/br23\asm/irq.h \
  include_lib/driver/cpu/br23\asm/hwi.h \
  include_lib/system\generic/printf.h \
  include_lib/system/generic/typedef.h include_lib\system/generic/log.h \
  include_lib/system\generic/errno-base.h \
  C:/JL/pi32/pi32v2-include\string.h C:/JL/pi32/pi32v2-include/_ansi.h \
  C:/JL/pi32/pi32v2-include\newlib.h \
  C:/JL/pi32/pi32v2-include\sys/config.h \
  C:/JL/pi32/pi32v2-include\machine/ieeefp.h \
  C:/JL/pi32/pi32v2-include\sys/features.h \
  C:/JL/pi32/pi32v2-include\sys/reent.h \
  C:/JL/pi32/pi32v2-include\sys/_types.h \
  C:/JL/pi32/pi32v2-include\machine/_types.h \
  C:/JL/pi32/pi32v2-include\machine/_default_types.h \
  C:/JL/pi32/pi32v2-include\sys/lock.h \
  C:/JL/pi32/pi32v2-include\sys/cdefs.h \
  C:/JL/pi32/pi32v2-include\sys/string.h \
  C:/JL/pi32/pi32v2-include\strings.h \
  C:/JL/pi32/pi32v2-include\sys/types.h \
  C:/JL/pi32/pi32v2-include\sys/_stdint.h \
  C:/JL/pi32/pi32v2-include\machine/types.h include_lib\system/malloc.h \
  apps/soundbox/include\app_config.h \
  include_lib/driver/cpu/br23\asm/clock_define.h \
  apps/soundbox/board/br23\board_config.h \
  apps/soundbox/board/br23/board_ac6954a_demo/board_ac6954a_demo_cfg.h \
  apps/soundbox/board/br23/board_ac695x_demo/board_ac695x_demo_cfg.h \
  apps/common/usb\usb_std_class_def.h \
  apps/soundbox/board/br23/board_ac6951_kgb_v1/board_ac6951_kgb_cfg.h \
  apps/soundbox/board/br23/board_ac6952e_lighter/board_ac6952e_lighter_cfg.h \
  apps/soundbox/board/br23/board_ac6955f_headset_mono/board_ac6955f_headset_mono_cfg.h \
  apps/soundbox/board/br23/board_ac695x_charging_bin/board_ac695x_charging_bin.h \
  apps/soundbox/board/br23/board_ac695x_btemitter/board_ac695x_btemitter.h \
  apps/soundbox/board/br23/board_ac695x_tws_box/board_ac695x_tws_box.h \
  apps/soundbox/board/br23/board_ac695x_tws/board_ac695x_tws.h \
  apps/soundbox/board/br23/board_ac695x_multimedia_charging_bin/board_ac695x_multimedia_charging_bin.h \
  apps/soundbox/board/br23/board_ac695x_soundcard/board_ac695x_soundcard.h \
  apps/soundbox/board/br23/board_ac695x_smartbox/board_ac695x_smartbox.h \
  apps/soundbox/board/br23/board_ac695x_lcd/board_ac695x_lcd_cfg.h \
  apps/soundbox/board/br23/board_ac695x_cvp_develop/board_ac695x_cvp_develop_cfg.h \
  apps/soundbox/board/br23/board_ac695x_audio_effects/board_ac695x_audio_effects_cfg.h \
  apps/soundbox/board/br23/board_ac695x_megaphone/board_ac695x_megaphone_cfg.h \
  apps/soundbox/board/br23/board_ac6951g/board_ac6951g_cfg.h \
  apps/soundbox/board/br23/board_ac6083a/board_ac6083a_cfg.h \
  apps/soundbox/board/br23/board_ac6083a_iap/board_ac6083a_iap_cfg.h \
  include_lib/btctrler\btcontroller_mode.h \
  apps/soundbox/include/user_cfg_id.h apps/common/usb\usb_common_def.h \
  apps/soundbox/include\smartbox/smartbox.h \
  apps/soundbox/include\smartbox/config.h \
  apps/common/third_party_profile/jieli\le_common.h \
  C:/JL/pi32/pi32v2-include\stdint.h \
  C:/JL/pi32/pi32v2-include\sys/_intsup.h \
  include_lib\btstack/bluetooth.h \
  include_lib/btstack/le/ble_data_types.h \
  include_lib/btstack/le/ble_api.h include_lib\btstack/btstack_typedef.h \
  include_lib/btstack/le/le_user.h include_lib/btstack/le/att.h \
  include_lib/btstack/le/gatt.h include_lib/btstack/le/sm.h \
  include_lib/btstack/btstack_event.h \
  include_lib/btstack/third_party/common\spp_user.h \
  include_lib/btstack/third_party/common\ble_user.h \
  include_lib/btstack/third_party/common\btstack_3th_protocol_user.h \
  include_lib/btstack/third_party/common/spp_config.h \
  include_lib/btstack/third_party/common/ble_config.h \
  include_lib/btstack/third_party/rcsp\JL_rcsp_api.h \
  include_lib/driver/device\uart.h include_lib/system\device/device.h \
  include_lib/system\generic/list.h include_lib/system\generic/atomic.h \
  include_lib/system/generic/cpu.h include_lib/system/generic/irq.h \
  include_lib/system\device/ioctl_cmds.h \
  include_lib/system\generic/ioctl.h include_lib\system/task.h \
  include_lib/system/os/os_api.h include_lib/system\os/os_cpu.h \
  include_lib/system/generic\jiffies.h include_lib/system\os/os_error.h \
  include_lib/system\os/os_type.h \
  include_lib/system/os/FreeRTOS/FreeRTOS.h \
  include_lib/system/os/FreeRTOS/FreeRTOSConfig.h \
  include_lib/system/os/FreeRTOS/pi32v2/portmacro.h \
  include_lib/system/os/FreeRTOS/projdefs.h \
  include_lib/system/os/FreeRTOS/portable.h \
  include_lib/system/os/FreeRTOS/deprecated_definitions.h \
  include_lib/system/os/FreeRTOS/mpu_wrappers.h \
  include_lib/system/os/FreeRTOS/semphr.h \
  include_lib/system/os/FreeRTOS/queue.h \
  include_lib/system/os/FreeRTOS/task.h \
  include_lib/system/os/FreeRTOS/list.h \
  include_lib/btstack/third_party/rcsp\JL_rcsp_protocol.h \
  include_lib/btstack/third_party/rcsp/JL_rcsp_packet.h \
  include_lib/btstack/third_party/rcsp\attr.h \
  apps/soundbox/include\smartbox/function.h \
  apps/soundbox/include\smartbox/cmd_user.h \
  apps/soundbox/include\smartbox/smartbox_task.h \
  apps/common/dev_manager\dev_manager.h include_lib\system/includes.h \
  include_lib/system/init.h include_lib/system/event.h \
  include_lib/system/generic/rect.h include_lib/system/spinlock.h \
  include_lib/system/timer.h include_lib/system/wait.h \
  include_lib/system/app_core.h include_lib/system/app_msg.h \
  include_lib/system/database.h include_lib/system/fs/fs.h \
  include_lib\system/sys_time.h include_lib/system/fs/fs_file_name.h \
  include_lib/system/fs/sdfile.h include_lib/system/power_manage.h \
  include_lib/system/syscfg_id.h include_lib/system/bank_switch.h \
  include_lib/system/generic/includes.h \
  include_lib/system/generic/ascii.h include_lib/system/generic/gpio.h \
  include_lib/driver/cpu/br23\asm/gpio.h \
  include_lib/system/generic/version.h include_lib/system/generic/lbuf.h \
  include_lib/system/generic/lbuf_lite.h \
  include_lib/system/generic/circular_buf.h \
  include_lib/system/generic/index.h \
  include_lib/system/generic/debug_lite.h \
  include_lib/system/device/includes.h \
  include_lib/system/device/key_driver.h \
  include_lib/system/device/iokey.h include_lib/system/device/irkey.h \
  include_lib/system/device/adkey.h \
  include_lib/driver/cpu/br23\asm/adc_api.h \
  include_lib/system/device/slidekey.h \
  include_lib/system/device/touch_key.h \
  include_lib/driver/cpu/br23\asm/plcnt.h \
  include_lib/system/device/rdec_key.h \
  include_lib/driver/cpu/br23\asm/rdec.h \
  include_lib/driver/cpu/br23\asm/includes.h \
  include_lib/driver/cpu/br23\asm/crc16.h \
  include_lib/driver/cpu/br23\asm/clock.h \
  include_lib/driver/cpu/br23\asm/clock_hw.h \
  include_lib/driver/cpu/br23\asm/uart.h \
  include_lib/driver/cpu/br23\asm/uart_dev.h \
  include_lib/driver/cpu/br23\asm/spiflash.h \
  include_lib/driver\device/spiflash.h \
  include_lib/driver/cpu/br23\asm/power_interface.h \
  include_lib/driver/cpu/br23\asm/efuse.h \
  include_lib/driver/cpu/br23\asm/wdt.h \
  include_lib/driver/cpu/br23\asm/timer.h \
  include_lib/driver\device/sdio_host_init.h \
  include_lib/system/crypto_toolbox/crypto.h \
  include_lib/system/crypto_toolbox/endian.h \
  include_lib/system/crypto_toolbox/Crypto_hash.h \
  include_lib/system/crypto_toolbox/hmac.h \
  include_lib/system/crypto_toolbox/sha256.h \
  include_lib/system/crypto_toolbox/bigint.h \
  include_lib/system/crypto_toolbox/bigint_impl.h \
  include_lib/system/crypto_toolbox/ecdh.h \
  include_lib/system/crypto_toolbox/micro-ecc/uECC_new.h \
  include_lib/system/crypto_toolbox/aes_cmac.h \
  include_lib/system/crypto_toolbox/rijndael.h \
  apps/common/music\music_player.h \
  include_lib/system\server/server_core.h \
  include_lib/media/media_develop\media/audio_decoder.h \
  include_lib/media/media_develop\media/audio_base.h \
  include_lib\media/audio_stream.h \
  apps/common\file_operate/file_manager.h \
  cpu/br23\audio_dec/audio_dec_file.h \
  include_lib/media/media_develop\media/includes.h \
  include_lib/media/media_develop\media/audio_encoder.h \
  include_lib/media/media_develop\media/mixer.h \
  include_lib/media/media_develop/media/application/audio_buf_sync.h \
  include_lib/media/media_develop/media/cpu/br23\asm/cpu_includes.h \
  include_lib/media/media_develop/media/cpu/br23\asm/audio_adc.h \
  include_lib/media/media_develop/media/cpu/br23\asm/audio_linein.h \
  include_lib/media/media_develop/media/cpu/br23\asm/audio_src.h \
  include_lib/media/media_develop/media\audio_resample.h \
  include_lib/media/media_develop/media/cpu/br23/asm/dac.h \
  include_lib/media/media_develop\media/audio_cfifo.h \
  include_lib/media/media_develop/media/cpu/br23\asm/hw_eq.h \
  include_lib\media/eq_func_define.h \
  include_lib/media/media_develop/media/cpu/br23/asm/audio_sync.h \
  include_lib/media/media_develop/media/cpu/br23/asm/audio_iis.h \
  include_lib/media/media_develop/media/mixer_cyclic.h \
  include_lib/media/media_develop\media/automute.h \
  include_lib/media/media_develop/media/effectrs_sync.h \
  include_lib/media/media_develop/media/cpu/br23\asm/audio_spdif.h \
  include_lib/media/media_develop\media/file_decoder.h \
  apps/common\music/music_decrypt.h apps/common\music/music_id3.h \
  include_lib/media/media_develop/media\application/audio_vocal_tract_synthesis.h \
  include_lib/media/media_develop/media\application/audio_pitchspeed.h \
  include_lib/media/media_develop/media/cpu/br23\asm/ps_cal_api.h \
  include_lib\media/audio_surround.h include_lib\media/effect_sur_api.h \
  include_lib\media/audio_vbass.h include_lib\media/VirtualBass_api.h \
  include_lib\media/effects_adj.h \
  include_lib/system\config/config_interface.h \
  include_lib/system\config/config_transport.h \
  include_lib/system\config/config_target.h include_lib\media/audio_eq.h \
  include_lib\media/audio_drc.h include_lib\media/sw_drc.h \
  include_lib\media/drc_api.h include_lib\media/howling_api.h \
  include_lib\media/howling_pitchshifter_api.h \
  include_lib/media/media_develop/media/cpu/br23/asm\pemafrow_api.h \
  include_lib\media/DynamicEQ_api.h \
  include_lib\media/DynamicEQ_Detection_api.h \
  include_lib\media/reverb_api.h include_lib\media/noisegate_api.h \
  include_lib\media/audio_gain_process.h \
  include_lib\media/voiceChanger_api.h \
  cpu/br23\audio_effect/audio_dynamic_eq_demo.h \
  include_lib\media/dynamic_eq.h \
  cpu/br23\audio_effect/audio_eff_default_parm.h \
  apps/soundbox/include/task_manager\music/music.h \
  apps/common\file_operate/file_bs_deal.h \
  apps/common\storage_dev/storage_dev.h \
  apps/soundbox/include\smartbox/event.h \
  apps/soundbox/include\common/user_msg.h
