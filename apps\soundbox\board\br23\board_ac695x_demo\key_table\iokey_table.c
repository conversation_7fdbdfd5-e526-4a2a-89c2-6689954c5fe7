#include "key_event_deal.h"
#include "key_driver.h"
#include "app_config.h"
#include "board_config.h"
#include "app_task.h"

#ifdef CONFIG_BOARD_AC695X_DEMO
/***********************************************************
 *				bt 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_BT_EN
const u16 bt_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
    [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },

};
#endif

/***********************************************************
 *				fm 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_FM_EN
const u16 fm_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				linein 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_LINEIN_EN
const u16 linein_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				music 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_MUSIC_EN
const u16 music_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				pc 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_PC_EN
const u16 pc_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				record 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_RECORD_EN
const u16 record_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				rtc 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_RTC_EN
const u16 rtc_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				spdif 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_SPDIF_EN
const u16 spdif_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif
/***********************************************************
 *				walkie 模式的 iokey table
 ***********************************************************/
#if TCFG_APP_WALKIE_EN
const u16 walkie_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif

/***********************************************************
 *				idle 模式的 iokey table
 ***********************************************************/
const u16 idle_key_io_table[KEY_IO_NUM_MAX][KEY_EVENT_MAX] = {
    //单击             //长按          //hold         //抬起            //双击                //三击
     [0] = {
        KEY_MUSIC_PP,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL,	KEY_NULL
    },
};
#endif
