/*
 * FFT硬件加速测试集成指南
 * 
 * 作者：音频算法测试团队
 * 日期：2025-08-06
 * 版本：v2.0
 */

# FFT硬件加速测试系统集成指南

## 概述
本测试系统为DS888有线分机提供了全面的FFT硬件加速功能验证，包括音频处理、回音消除(AEC)、性能评估等多个测试模块。

## 新增功能 (v2.0)

### 1. 性能基准测试 (`fft_hardware_benchmark_test`)
- **功能**: 评估FFT硬件在连续工作条件下的性能表现
- **测试内容**:
  - 连续100轮FFT/IFFT性能测试
  - 平均处理时间计算
  - 实时处理能力评估
- **适用场景**: 产品性能验证、优化效果评估

### 2. AEC专用测试 (`fft_hardware_aec_specialized_test`)
- **功能**: 针对回音消除应用的专项FFT测试
- **测试内容**:
  - AEC算法FFT需求验证
  - 回音抑制效果测试
  - 收敛特性分析
- **适用场景**: 通话质量优化、AEC参数调试

### 3. 错误恢复测试 (`fft_hardware_error_recovery_test`)
- **功能**: 验证系统在异常情况下的稳定性
- **测试内容**:
  - NULL指针输入处理
  - 无效参数检测
  - 内存分配失败恢复
- **适用场景**: 系统稳定性验证、异常处理测试

### 4. 音频质量评估 (`fft_hardware_audio_quality_test`)
- **功能**: 评估FFT处理对音频质量的影响
- **测试内容**:
  - THD (总谐波失真) 测量
  - SNR (信噪比) 计算
  - 频率响应平坦度测试
- **适用场景**: 音频质量验证、产品认证测试

## 使用方法

### 方法1: 交互式测试
```c
// 启动交互式测试菜单
fft_hardware_test_interactive_main();
```

### 方法2: 自动化测试
```c
// 执行全套自动测试
fft_hardware_test_auto_main();
```

### 方法3: 快速验证
```c
// 快速功能验证
fft_hardware_quick_test();
```

### 方法4: 单项测试
```c
// 单独执行性能测试
fft_hardware_benchmark_test();

// 单独执行AEC测试  
fft_hardware_aec_specialized_test();

// 单独执行错误恢复测试
fft_hardware_error_recovery_test();

// 单独执行音频质量测试
fft_hardware_audio_quality_test();
```

## 测试结果解读

### 性能指标
- **FFT处理时间**: < 1ms/帧 (512点@32kHz)
- **实时系数**: > 10倍 (处理速度 > 实时需求)
- **连续工作稳定性**: 100轮测试无异常

### 音频质量指标
- **THD**: < 0.1% (总谐波失真)
- **SNR**: > 90dB (信噪比)
- **频率响应**: ±0.5dB (20Hz-20kHz)

### AEC效果指标
- **ERLE**: > 30dB (回音抑制)
- **收敛时间**: < 2秒
- **稳态误差**: < -40dB

## 集成到现有项目

### 1. 在Makefile中添加测试文件
```makefile
# 添加到OBJS列表
$(objs)/apps/soundbox/test/fft_hardware_main_test.o \
$(objs)/apps/soundbox/test/fft_512_audio_test.o \
$(objs)/apps/soundbox/test/aec_fft_test.o \
```

### 2. 在应用代码中调用测试
```c
#include "test/fft_hardware_main_test.h"

// 在初始化阶段进行测试
void app_audio_init(void) {
    // 快速验证FFT硬件功能
    fft_hardware_quick_test();
    
    // 其他初始化代码...
}

// 或在调试模式下进行完整测试
#ifdef DEBUG_FFT_TEST
void debug_fft_full_test(void) {
    fft_hardware_test_auto_main();
}
#endif
```

### 3. 配置测试参数
可以通过修改以下宏定义来调整测试参数：
```c
// 在fft_512_audio_test.h中
#define FFT_TEST_SAMPLE_RATE    32000   // 采样率
#define FFT_TEST_BUFFER_SIZE    512     // FFT点数
#define FFT_TEST_PRECISION_LIMIT 0.001  // 精度阈值

// 在aec_fft_test.h中  
#define AEC_TEST_ECHO_DELAY     160     // 回音延迟(ms)
#define AEC_TEST_ERLE_TARGET    30      // ERLE目标值(dB)
```

## 注意事项

1. **内存使用**: 测试过程中会使用额外的缓冲区，确保系统有足够内存
2. **实时性**: 音频质量测试可能耗时较长，建议在产品测试阶段使用
3. **硬件依赖**: 确保hw_fft.h和MathFunc_fix.h库正确链接
4. **调试输出**: 测试结果通过printf输出，确保串口或调试通道正常

## 版本历史

### v2.0 (2025-08-06)
- 新增性能基准测试
- 新增AEC专用测试
- 新增错误恢复测试  
- 新增音频质量评估测试
- 完善交互式菜单

### v1.0 (2025-08-06)
- 基础512点FFT音频测试
- AEC基础FFT测试
- 交互式测试框架

## 技术支持
如有问题请联系音频算法测试团队。
