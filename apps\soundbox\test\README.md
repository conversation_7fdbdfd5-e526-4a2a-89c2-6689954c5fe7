# FFT/IFFT Hardware Acceleration Test Suite

## Overview

This comprehensive test suite validates hardware FFT/IFFT functionality with specialized focus on 512-point audio processing and AEC (Acoustic Echo Cancellation) applications. The test suite includes both basic FFT functionality tests and advanced audio processing scenarios.

## Test Modules

### 1. **512-Point FFT Audio Processing Test** (`fft_512_audio_test.c`)
- **Purpose**: Validates 512-point FFT for audio processing applications
- **Sample Rate**: 16kHz (optimal for speech processing)
- **Frame Length**: 32ms (512 samples)
- **Frequency Resolution**: ~31.25Hz per bin
- **Test Coverage**:
  - Basic FFT/IFFT functionality
  - Audio frequency range testing (DC - 8kHz)
  - Dynamic range and noise tolerance
  - Performance benchmarking
  - Mathematical acceleration functions

### 2. **AEC-Specific FFT Test** (`aec_fft_test.c`)
- **Purpose**: Tests FFT in acoustic echo cancellation scenarios
- **Algorithm**: Frequency-domain adaptive filtering (NLMS)
- **Test Coverage**:
  - Echo path modeling and convergence
  - ERLE (Echo Return Loss Enhancement) measurement
  - Frequency response analysis
  - Real-time performance validation
  - Mathematical function integration

### 3. **Comprehensive Test Suite** (`fft_hardware_main_test.c`)
- **Purpose**: Unified test runner with multiple modes
- **Modes**:
  - Interactive menu-driven testing
  - Automated full test suite
  - Quick validation for development

## Original FFT Tests

This test program also comprehensively tests hardware Real FFT/IFFT functionality with correct buffer sizes for 256-point and 512-point transforms.

## Key Features

### 1. Real FFT/IFFT Testing
- **Buffer Size Verification**: Tests correct buffer sizes for real FFT
- **256-point Real FFT**: Buffer size = (256/2+1)*2 = 258 ints
- **512-point Real FFT**: Buffer size = (512/2+1)*2 = 514 ints
- **1024-point Real FFT**: Buffer size = (1024/2+1)*2 = 1026 ints
- **Data Type Testing**: Zero, sine wave, random, and ramp data
- **Roundtrip Testing**: FFT -> IFFT data recovery verification
- **Performance Benchmarking**: Timing and throughput analysis

### 2. Buffer Size Education
- **Correct Formula**: (N/2+1)*2 for N-point real FFT
- **Practical Examples**:
  - `int tmpbuf[258];` for 256-point real FFT
  - `int tmpbuf[514];` for 512-point real FFT
  - `int tmpbuf[1026];` for 1024-point real FFT
- **Memory Layout**: [real0, imag0, real1, imag1, ...]

### 3. Performance Analysis
- **Timing Measurements**: Precise millisecond timing
- **Throughput Calculation**: Operations per second
- **Error Analysis**: Reconstruction error statistics

## Real FFT Buffer Size Reference

### Critical Buffer Size Information

**For Real FFT, the buffer size is NOT simply N*2!**

| FFT Size | Input | Output | Buffer Size | Declaration |
|----------|-------|--------|-------------|-------------|
| 256-point | 256 real | 129 complex | 258 ints | `int tmpbuf[258];` |
| 512-point | 512 real | 257 complex | 514 ints | `int tmpbuf[514];` |

### Formula: `(N/2+1)*2`
- N-point real FFT produces (N/2+1) complex frequency bins
- Each complex number needs 2 ints (real + imaginary)
- Total buffer size = (N/2+1) × 2

### Test Data Types

| Type | Description | Purpose |
|------|-------------|---------|
| zero | All zero data | Test zero input handling |
| sine | Sine wave | Test real signal processing |
| random | Random data | Test general case |
| ramp | Linear ramp | Test DC and low frequency |

## Usage

### 1. Automatic Execution (Recommended)
When AEC algorithm is disabled, the system automatically runs Real FFT tests:
```c
// In walkie.c, automatically runs when AEC is disabled
fft_test_main();
```

### 2. Manual Invocation
```c
#include "test/fft_test.h"

void your_function() {
    fft_test_main();  // Run complete Real FFT test suite
}
```

### 3. Buffer Declaration Examples
```c
// For 256-point real FFT
int tmpbuf[258];  // (256/2+1)*2 = 258

// For 512-point real FFT
int tmpbuf[514];  // (512/2+1)*2 = 514

// General formula for N-point real FFT
int tmpbuf[(N/2+1)*2];
```

## 测试报告解读

### Expected Output Example (Simple FFT Test)
```
AEC disabled, starting Real FFT/IFFT hardware test...
[FFT] === Real FFT Hardware Test ===
[FFT] Buffer Size Guide:
[FFT]   256-point: int tmpbuf[258];
[FFT]   512-point: int tmpbuf[514];
[FFT] Formula: (N/2+1)*2 for N-point real FFT
[FFT]
[FFT] Testing 256-point Real FFT (N=256, Buffer=258)
[FFT] FFT completed in 2 ms
[FFT] IFFT completed in 2 ms
[FFT] Test PASSED
[FFT] Testing 512-point Real FFT (N=512, Buffer=514)
[FFT] FFT completed in 4 ms
[FFT] IFFT completed in 4 ms
[FFT] Test PASSED

[FFT] === Performance Test ===
[FFT] 256-point FFT: 18 ms total, 1 ms avg (10 iterations)
[FFT]
[FFT] === Test Summary ===
[FFT] Total: 2, Passed: 2, Failed: 0
[FFT] Success Rate: 100%
[FFT] SUCCESS: All FFT tests passed!
[FFT]
[FFT] === Buffer Size Reminder ===
[FFT] 256-point real FFT: int tmpbuf[258];
[FFT] 512-point real FFT: int tmpbuf[514];
[FFT] General formula: int tmpbuf[(N/2+1)*2];
[FFT] === Test Complete ===
Real FFT/IFFT test completed
```

## 性能参考数据

### 典型性能指标
- **FFT 256点**: ~1.8ms/次, ~556次/秒
- **IFFT 256点**: ~1.8ms/次, ~556次/秒
- **往返误差**: <1000 (可接受范围)
- **数据范围**: 建议±16383，极限±32767

### 定点数格式建议
- **Q8**: 精度1/256, 适合粗略计算
- **Q12**: 精度1/4096, 适合一般音频处理
- **Q16**: 精度1/65536, 适合高精度计算

## 故障排除

### 常见问题
1. **测试失败**: 检查硬件FFT模块是否正确初始化
2. **数值溢出**: 减小输入数据范围
3. **性能差**: 检查系统负载和时钟频率
4. **往返误差大**: 可能是定点数精度问题

### 调试建议
1. 先运行基础测试（零值、小数值）
2. 逐步增加数据范围
3. 检查输入数据格式是否正确
4. 验证FFT配置参数

## File Structure
```
apps/soundbox/test/
├── simple_fft_test.c   # Simple FFT test (minimal dependencies) ⭐ RECOMMENDED
├── extreme_fft_test.c  # Comprehensive FFT + MathFunc_fix testing ⭐ ADVANCED
├── real_fft_test.c     # Full-featured Real FFT test implementation
├── fft_test.c          # Wrapper file (includes real_fft_test.c)
├── fft_test.h          # Header file
├── fft_buffer_sizes.h  # Buffer size definitions and constants
├── compile_test.c      # Compilation verification test
├── Makefile            # Enhanced build configuration
├── build_test.sh       # Build test script
├── BUFFER_SIZE_GUIDE.md # Detailed buffer size guide
└── README.md           # Documentation
```

### New Audio Processing Tests

#### File Descriptions
- **fft_512_audio_test.c/.h**: ⭐ **NEW** - 512-point FFT audio processing specialized tests
- **aec_fft_test.c/.h**: ⭐ **NEW** - AEC (Acoustic Echo Cancellation) FFT application tests
- **fft_hardware_main_test.c/.h**: ⭐ **NEW** - Unified test suite with interactive and automated modes

#### 512-Point Audio Test Features
- **Precision Testing**: FFT/IFFT roundtrip accuracy validation
- **Audio Range Coverage**: Complete frequency spectrum testing (DC-8kHz)
- **Signal Quality**: SNR, THD, and dynamic range analysis
- **Performance Metrics**: Real-time processing capability assessment
- **Math Acceleration**: Hardware sin/cos/atan function performance

#### AEC Test Features
- **Convergence Analysis**: Adaptive filter convergence time and stability
- **ERLE Measurement**: Echo Return Loss Enhancement calculation
- **Frequency Response**: Multi-tone echo cancellation performance
- **CPU Usage**: Real-time processing overhead assessment
- **Algorithm Validation**: Complete frequency-domain AEC simulation

### Original FFT Tests

### File Descriptions
- **simple_fft_test.c**: ⭐ **RECOMMENDED** - Minimal FFT test with printf logging, no complex dependencies
- **extreme_fft_test.c**: ⭐ **ADVANCED** - Comprehensive FFT + MathFunc_fix testing with precision validation
- **real_fft_test.c**: Complete Real FFT test implementation with advanced features
- **fft_test.c**: Simple wrapper that includes real_fft_test.c for compatibility
- **fft_test.h**: Header file declaring fft_test_main() function
- **fft_buffer_sizes.h**: Critical buffer size definitions and constants
- **compile_test.c**: Compilation and runtime verification tests
- **Makefile**: Enhanced makefile supporting multiple targets and build options
- **build_test.sh**: Script to test all build targets and verify functionality
- **BUFFER_SIZE_GUIDE.md**: Comprehensive buffer size guide with examples
- **README.md**: Complete documentation

### Recommended Usage: simple_fft_test.c
This is the **recommended version** because:
- ✅ Minimal dependencies (no complex logging system)
- ✅ Uses standard printf for output
- ✅ Compiles easily in embedded environments
- ✅ Tests both 256-point and 512-point Real FFT
- ✅ Includes performance benchmarking
- ✅ Clear buffer size demonstrations

## Build and Run

### New Audio Processing Tests
```bash
# Run 512-point audio processing test
fft_512_audio_test_main();

# Run AEC-specific FFT test
aec_fft_test_main();

# Run complete test suite (interactive)
fft_hardware_test_interactive_main();

# Run complete test suite (automated)
fft_hardware_test_auto_main();

# Quick validation test
fft_hardware_quick_test();
```

### Quick Build (Recommended)
```bash
cd apps/soundbox/test
make clean
make simple_fft    # Build the recommended simple version
```

### Available Build Targets
```bash
# Build all targets
make all

# Build simple FFT test (RECOMMENDED)
make simple_fft

# Build extreme FFT + MathFunc_fix test (ADVANCED)
make extreme_fft

# Build full-featured real FFT test
make real_fft

# Build wrapper
make fft_test

# Clean build artifacts
make clean

# Show help with buffer size info
make help
```

## Build and Run

### Quick Build (Recommended)
```bash
cd apps/soundbox/test
make clean
make simple_fft    # Build the recommended simple version
```

### Available Build Targets
```bash
# Build all targets
make all

# Build simple FFT test (RECOMMENDED)
make simple_fft

# Build extreme FFT + MathFunc_fix test (ADVANCED)
make extreme_fft

# Build full-featured real FFT test
make real_fft

# Build wrapper
make fft_test

# Clean build artifacts
make clean

# Show help with buffer size info
make help

# Test header compilation
make compile-test

# Run build test script
chmod +x build_test.sh
./build_test.sh
```

### Build Output
- `simple_fft_test` - ⭐ **RECOMMENDED** - Minimal dependencies, printf logging
- `real_fft_test` - Full-featured test with advanced logging
- `fft_test` - Wrapper executable (includes real_fft_test.c)

## Important Notes

### Buffer Size Critical Information
1. **NEVER use N*2 for real FFT buffer size**
2. **ALWAYS use (N/2+1)*2 for real FFT buffer size**
3. **Examples:**
   - 128-point real FFT: `int tmpbuf[130];`  // (128/2+1)*2 = 130
   - 256-point real FFT: `int tmpbuf[258];`  // (256/2+1)*2 = 258
   - 512-point real FFT: `int tmpbuf[514];`  // (512/2+1)*2 = 514

### Test Execution Notes
1. Tests consume system resources and time
2. Recommend running during system idle time
3. Results output via log_info (serial port)
4. Some tests may take several seconds to complete

### Real FFT vs Complex FFT
- **Real FFT**: Input N real samples, output (N/2+1) complex samples
- **Complex FFT**: Input N complex samples, output N complex samples
- **Buffer difference**: Real FFT saves memory by exploiting symmetry

## Audio Processing & AEC Applications

### 512-Point FFT Audio Configuration

#### Optimal Settings for Speech Processing
```c
#define AUDIO_SAMPLE_RATE    16000   // 16kHz sampling
#define FFT_SIZE            512     // 32ms frame length
#define FREQ_RESOLUTION     31.25   // Hz per frequency bin
#define OVERLAP_PERCENT     50      // 50% frame overlap
#define WINDOW_TYPE         HANN    // Hann window recommended
```

#### Audio Quality Metrics
- **Dynamic Range**: 96dB (16-bit audio)
- **Frequency Coverage**: DC to 8kHz (Nyquist)
- **Time Resolution**: 32ms frames
- **Processing Latency**: < 2ms per frame
- **CPU Usage**: < 25% for real-time processing

### AEC (Acoustic Echo Cancellation) Application

#### Algorithm Configuration
```c
#define AEC_FILTER_LENGTH    128     // 8ms echo path modeling
#define AEC_STEP_SIZE       0.01     // NLMS adaptation rate
#define AEC_LEAKAGE_FACTOR  0.99     // Coefficient leakage
#define TARGET_ERLE_DB      20       // Target echo suppression
#define CONVERGENCE_TIME_MS 2000     // Maximum convergence time
```

#### Performance Expectations
- **ERLE (Echo Return Loss Enhancement)**: > 20dB
- **Convergence Time**: < 2 seconds
- **Frequency Response**: 200Hz - 6kHz effective range
- **Double-talk Handling**: Adaptive step-size control
- **Computational Load**: < 50% CPU usage

#### Mathematical Acceleration Benefits
- **sin/cos Functions**: 10x faster than software implementation
- **atan Functions**: 5x faster phase calculations
- **Complex Arithmetic**: Hardware-accelerated FFT operations
- **Real-time Feasibility**: Enables 16kHz full-duplex processing

### Recommended Usage Patterns

#### For Voice Enhancement
1. Use 512-point real FFT with 50% overlap
2. Apply spectral subtraction in frequency domain
3. Use hardware sin/cos for window functions
4. Target < 10ms total processing latency

#### For Echo Cancellation
1. Implement frequency-domain adaptive filtering
2. Use 512-point FFT for 32ms frames
3. Maintain separate near-end and far-end buffers
4. Monitor ERLE for convergence assessment

#### For Noise Reduction
1. Estimate noise spectrum during silence periods
2. Apply Wiener filtering in frequency domain
3. Use hardware math functions for gain calculations
4. Implement spectral floor to prevent artifacts

### Integration Guidelines

#### In walkie.c Application
```c
// Include the new test headers
#include "test/fft_512_audio_test.h"
#include "test/aec_fft_test.h"

// Run tests during initialization
void walkie_fft_validation(void) {
    // Quick validation
    fft_hardware_quick_test();
    
    // Full audio processing validation
    fft_512_audio_test_main();
    
    // AEC-specific validation
    aec_fft_test_main();
}
```

#### Performance Monitoring
- Monitor CPU usage during real-time operation
- Track convergence metrics for adaptive algorithms
- Validate audio quality through SNR measurements
- Ensure stable operation across temperature ranges

This comprehensive test suite validates that the hardware FFT implementation meets the stringent requirements for professional audio processing and acoustic echo cancellation applications.
