# Real FFT/IFFT Hardware Test Makefile
# Supports both real_fft_test and fft_test targets

# Compiler settings for embedded environment
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS =

# Source files
REAL_FFT_SOURCES = real_fft_test.c
FFT_TEST_SOURCES = fft_test.c
SIMPLE_FFT_SOURCES = simple_fft_test.c
MINIMAL_FFT_SOURCES = minimal_fft_test.c
EXTREME_FFT_SOURCES = extreme_fft_test.c
ALL_SOURCES = $(REAL_FFT_SOURCES) $(FFT_TEST_SOURCES) $(SIMPLE_FFT_SOURCES) $(MINIMAL_FFT_SOURCES) $(EXTREME_FFT_SOURCES)

# Header files
HEADERS = fft_test.h fft_buffer_sizes.h

# Target executables
REAL_FFT_TARGET = real_fft_test
FFT_TEST_TARGET = fft_test
SIMPLE_FFT_TARGET = simple_fft_test
MINIMAL_FFT_TARGET = minimal_fft_test
EXTREME_FFT_TARGET = extreme_fft_test
ALL_TARGETS = $(REAL_FFT_TARGET) $(FFT_TEST_TARGET) $(SIMPLE_FFT_TARGET) $(MINIMAL_FFT_TARGET) $(EXTREME_FFT_TARGET)

# Include paths for embedded system
INCLUDES = -I../../../include \
           -I../../../cpu/br23 \
           -I../../../system/includes \
           -I../../../media/includes \
           -I../../../apps/common \
           -I../../../apps/soundbox

# Libraries (if needed for embedded system)
LIBS = -lm

# Default target - build both
all: $(ALL_TARGETS)

# Real FFT test target
$(REAL_FFT_TARGET): $(REAL_FFT_SOURCES) $(HEADERS)
	@echo "Building Real FFT test..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $(REAL_FFT_TARGET) $(REAL_FFT_SOURCES) $(LIBS)
	@echo "Real FFT test built successfully!"
	@echo "Buffer sizes: 256-point=258, 512-point=514"

# FFT test wrapper target
$(FFT_TEST_TARGET): $(FFT_TEST_SOURCES) $(HEADERS)
	@echo "Building FFT test wrapper..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $(FFT_TEST_TARGET) $(FFT_TEST_SOURCES) $(LIBS)
	@echo "FFT test wrapper built successfully!"
	@echo "Buffer sizes: 256-point=258, 512-point=514"

# Simple FFT test target (minimal dependencies)
$(SIMPLE_FFT_TARGET): $(SIMPLE_FFT_SOURCES)
	@echo "Building Simple FFT test..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $(SIMPLE_FFT_TARGET) $(SIMPLE_FFT_SOURCES) $(LIBS)
	@echo "Simple FFT test built successfully!"
	@echo "This version uses minimal dependencies and printf logging"

# Extreme FFT test target (comprehensive testing)
$(EXTREME_FFT_TARGET): $(EXTREME_FFT_SOURCES)
	@echo "Building Extreme FFT test..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $(EXTREME_FFT_TARGET) $(EXTREME_FFT_SOURCES) $(LIBS)
	@echo "Extreme FFT test built successfully!"
	@echo "This version tests extreme inputs and data format validation"

# Individual targets
real_fft: $(REAL_FFT_TARGET)
fft_test: $(FFT_TEST_TARGET)
simple_fft: $(SIMPLE_FFT_TARGET)
extreme_fft: $(EXTREME_FFT_TARGET)

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(ALL_TARGETS) *.o *.d
	@echo "Clean completed!"

# Install/copy to target directory (if needed)
install: all
	@echo "Installing test programs..."
	@mkdir -p ../../../bin/test
	cp $(ALL_TARGETS) ../../../bin/test/
	@echo "Installation completed!"

# Run tests (for development)
test: $(REAL_FFT_TARGET)
	@echo "Running Real FFT tests..."
	./$(REAL_FFT_TARGET)

# Compile test for header files
compile-test:
	@echo "Testing header file compilation..."
	$(CC) $(CFLAGS) $(INCLUDES) -DCOMPILE_TEST_MAIN -o compile_test compile_test.c
	@echo "Header compilation test passed!"
	./compile_test && echo "Runtime buffer size tests passed!" || echo "Runtime tests failed!"
	rm -f compile_test

# Help target
help:
	@echo "=== Real FFT Test Build System ==="
	@echo ""
	@echo "Available targets:"
	@echo "  all        - Build all FFT test programs"
	@echo "  real_fft   - Build only real_fft_test (full featured)"
	@echo "  fft_test   - Build only fft_test wrapper"
	@echo "  simple_fft - Build only simple_fft_test (minimal deps)"
	@echo "  extreme_fft - Build only extreme_fft_test (comprehensive testing)"
	@echo "  clean      - Remove build artifacts"
	@echo "  install    - Install to bin/test directory"
	@echo "  test       - Run real_fft_test"
	@echo "  compile-test - Test header file compilation and buffer sizes"
	@echo "  help       - Show this help message"
	@echo ""
	@echo "=== Real FFT Buffer Sizes ==="
	@echo "CRITICAL: Use correct buffer sizes for Real FFT!"
	@echo "  128-point Real FFT: int tmpbuf[130];  // (128/2+1)*2"
	@echo "  256-point Real FFT: int tmpbuf[258];  // (256/2+1)*2"
	@echo "  512-point Real FFT: int tmpbuf[514];  // (512/2+1)*2"
	@echo "  Formula: tmpbuf[(N/2+1)*2] for N-point Real FFT"
	@echo ""
	@echo "Files:"
	@echo "  real_fft_test.c     - Main implementation"
	@echo "  fft_test.c          - Wrapper (includes real_fft_test.c)"
	@echo "  fft_buffer_sizes.h  - Buffer size definitions"
	@echo "  fft_test.h          - Header file"

# Dependency tracking (optional)
-include $(ALL_SOURCES:.c=.d)

%.d: %.c
	@$(CC) $(CFLAGS) $(INCLUDES) -MM $< > $@

.PHONY: all clean install test compile-test help real_fft fft_test simple_fft extreme_fft
