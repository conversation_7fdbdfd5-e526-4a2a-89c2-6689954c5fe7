/*
 * FFT/IFFT 512点硬件加速音频处理专项测试头文件
 * 
 * 作者：音频测试工程师
 * 日期：2025-08-06
 */

#ifndef FFT_512_AUDIO_TEST_H
#define FFT_512_AUDIO_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 主测试函数入口
 * 
 * 功能：
 * 1. 512点FFT/IFFT精度测试，验证音频处理适用范围
 * 2. 硬件加速数学函数性能测试
 * 3. 回音消除算法应用场景测试
 * 4. 音频频率响应分析
 * 5. 动态范围和信噪比测试
 * 6. 性能基准测试
 * 
 * 测试配置：
 * - FFT大小: 512点 (适合16kHz采样率下32ms音频帧)
 * - 频率分辨率: ~31.25Hz
 * - 音频范围: DC - 8kHz
 * - 精度要求: <0.1%误差，>40dB SNR
 * - 性能要求: <25% CPU使用率
 */
void fft_512_audio_test_main(void);

/*
 * FFT 512点性能基准测试
 * 测试不同负载条件下的处理性能
 */
void fft_512_audio_performance_test(void);

/*
 * FFT 512点精度测试
 * 验证正弦波信号的FFT精度
 */
void fft_512_audio_precision_test(void);

/*
 * FFT 512点动态范围测试
 * 测试复合音频信号的处理范围
 */
void fft_512_audio_range_test(void);

#ifdef __cplusplus
}
#endif

#endif // FFT_512_AUDIO_TEST_H
