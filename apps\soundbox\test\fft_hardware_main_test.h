/*
 * FFT硬件加速综合测试主程序头文件
 * 
 * 作者：音频算法测试团队
 * 日期：2025-08-06
 */

#ifndef FFT_HARDWARE_MAIN_TEST_H
#define FFT_HARDWARE_MAIN_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 交互式测试主函数
 * 提供菜单选择不同的测试项目
 */
void fft_hardware_test_interactive_main(void);

/*
 * 非交互式自动测试主函数
 * 自动执行全套测试，适合集成测试环境
 */
void fft_hardware_test_auto_main(void);

/*
 * 快速验证测试函数
 * 执行核心功能验证，适合开发调试
 */
void fft_hardware_quick_test(void);

/*
 * 性能基准测试函数
 * 测试FFT/IFFT在不同条件下的性能表现
 */
void fft_hardware_benchmark_test(void);

/*
 * 回音消除专用测试函数
 * 针对AEC应用场景的专项测试
 */
void fft_hardware_aec_specialized_test(void);

/*
 * 错误恢复测试函数
 * 测试异常情况下的错误处理和恢复能力
 */
void fft_hardware_error_recovery_test(void);

/*
 * 音频质量评估测试函数
 * 评估FFT处理对音频质量的影响
 */
void fft_hardware_audio_quality_test(void);

#ifdef __cplusplus
}
#endif

#endif // FFT_HARDWARE_MAIN_TEST_H
