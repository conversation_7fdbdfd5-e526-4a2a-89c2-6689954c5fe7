/*
 * Real FFT Buffer Size Definitions
 *
 * CRITICAL: For Real FFT, buffer size is NOT N*2!
 * Correct formula: (N/2+1)*2 for N-point real FFT
 *
 * This header provides the correct buffer size constants
 * for common Real FFT sizes used in audio processing.
 */

#ifndef FFT_BUFFER_SIZES_H
#define FFT_BUFFER_SIZES_H

#ifdef __cplusplus
extern "C" {
#endif

// Real FFT Buffer Size Calculations
// Formula: (N/2+1)*2 where N is the FFT size

// 128-point Real FFT
#define REAL_FFT_128_SIZE       128
#define REAL_FFT_128_LOG2       7
#define REAL_FFT_128_BUF_SIZE   130     // (128/2+1)*2 = (64+1)*2 = 130

// 256-point Real FFT
#define REAL_FFT_256_SIZE       256
#define REAL_FFT_256_LOG2       8
#define REAL_FFT_256_BUF_SIZE   258     // (256/2+1)*2 = (128+1)*2 = 258

// 512-point Real FFT
#define REAL_FFT_512_SIZE       512
#define REAL_FFT_512_LOG2       9
#define REAL_FFT_512_BUF_SIZE   514     // (512/2+1)*2 = (256+1)*2 = 514

// 1024-point Real FFT
#define REAL_FFT_1024_SIZE      1024
#define REAL_FFT_1024_LOG2      10
#define REAL_FFT_1024_BUF_SIZE  1026    // (1024/2+1)*2 = (512+1)*2 = 1026

// Buffer size calculation macro (simple version)
#define REAL_FFT_BUF_SIZE(N)    (((N)/2+1)*2)

// Usage examples (as comments for reference):
/*
// Correct buffer declarations:
int fft_128_buf[REAL_FFT_128_BUF_SIZE];    // int fft_128_buf[130];
int fft_256_buf[REAL_FFT_256_BUF_SIZE];    // int fft_256_buf[258];
int fft_512_buf[REAL_FFT_512_BUF_SIZE];    // int fft_512_buf[514];
int fft_1024_buf[REAL_FFT_1024_BUF_SIZE];  // int fft_1024_buf[1026];

// Generic buffer declaration:
int fft_buf[REAL_FFT_BUF_SIZE(256)];       // int fft_buf[258];

// WRONG - DO NOT USE:
int wrong_buf[256*2];                       // This is WRONG for real FFT!
int wrong_buf[512];                         // This is WRONG for 256-point real FFT!
*/

#ifdef __cplusplus
}
#endif

#endif // FFT_BUFFER_SIZES_H

/*
 * Quick Reference:
 * 
 * Real FFT Size | Buffer Size | Declaration
 * --------------|-------------|---------------------------
 * 128-point     | 130 ints    | int tmpbuf[130];
 * 256-point     | 258 ints    | int tmpbuf[258];
 * 512-point     | 514 ints    | int tmpbuf[514];
 * 1024-point    | 1026 ints   | int tmpbuf[1026];
 * 
 * Formula: tmpbuf[(N/2+1)*2] for N-point real FFT
 * 
 * Why this size?
 * - Real FFT of N points produces (N/2+1) complex frequency bins
 * - Each complex number needs 2 integers (real + imaginary)
 * - Total buffer size = (N/2+1) × 2
 * 
 * Memory layout: [real0, imag0, real1, imag1, real2, imag2, ...]
 */
