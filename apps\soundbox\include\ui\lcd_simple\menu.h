
//generated by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2011//

#ifndef _MENU_H_
#define _MENU_H_

////StringResID Define Table////
#define  Chinese_Simplified           1
#define  Chinese_Traditional           2
#define  Japanese           3
#define  Korean           4
#define  English           5
#define  French           6
#define  German           7
#define  Italian           8
#define  Dutch           9
#define  Portuguese           10
#define  Spanish           11
#define  Swedish           12
#define  Czech           13
#define  Danish           14
#define  Polish           15
#define  Russian           16
#define  Turkey           17
#define  Hebrew           18
#define  Thai           19
#define  Hungarian           20
#define  Romanian           21
#define  Arabic           22

#define  LANGUAGEID_SUM      22



////Multi Language Define Table////
#define  vplaym1           1
#define  mplay2           2
#define  mplay4           3
#define  mplay5           4
#define  vstopm1           5
#define  vstopm2           6
#define  udisk           7
#define  cdelfile           8
#define  cdelall           9
#define  mstop5           10
#define  vloopm1           11
#define  vloopm2           12
#define  vloopm3           13
#define  vloopm4           14
#define  vloopm5           15
#define  vloopm6           16
#define  meq1           17
#define  meq2           18
#define  meq3           19
#define  meq4           20
#define  meq5           21
#define  meq6           22
#define  meq7           23
#define  meq8           24
#define  recpara           25
#define  mrstop3           26
#define  rsetgain           27
#define  main2           28
#define  mbps           29
#define  msrate           30
#define  smain7           31
#define  mrbps0           32
#define  mrbps1           33
#define  mrbps2           34
#define  mrbps3           35
#define  mrbps4           36
#define  mrbps5           37
#define  mrbps6           38
#define  mrbps7           39
#define  mrbps8           40
#define  mrbps9           41
#define  mrbps10           42
#define  mrbps11           43
#define  srate48k           44
#define  srate32k           45
#define  srate24k           46
#define  srate16k           47
#define  srate12k           48
#define  record1           49
#define  record2           50
#define  record           51
#define  rsaving           52
#define  fmenu16           53
#define  sreplay1           54
#define  fmenu1           55
#define  fmenu15           56
#define  fmfreq           57
#define  fmenu2           58
#define  fmenu3           59
#define  smain1           60
#define  smain2           61
#define  smain3           62
#define  s_tools           63
#define  kvoice1           64
#define  kvoice2           65
#define  smain6           66
#define  smain8           67
#define  smain9           68
#define  slan1           69
#define  slan3           70
#define  slan4           71
#define  slan5           72
#define  slan2           73
#define  slan6           74
#define  slan7           75
#define  slan8           76
#define  slan9           77
#define  slan10           78
#define  slan11           79
#define  slan12           80
#define  slan13           81
#define  slan14           82
#define  slan15           83
#define  slan16           84
#define  slan17           85
#define  slan18           86
#define  slan19           87
#define  slan20           88
#define  slan21           89
#define  slan22           90
#define  smain4           91
#define  clock           92
#define  fmset           93
#define  alarmon           94
#define  alarmoff           95
#define  alarmtm           96
#define  alarmcyc           97
#define  alarmmus           98
#define  alarmvol           99
#define  ringonce           100
#define  ringday           101
#define  ringweek           102
#define  ringinl           103
#define  fm_rmon           104
#define  fm_rtue           105
#define  fm_rwed           106
#define  fm_rthu           107
#define  fm_rfri           108
#define  fm_rsat           109
#define  fm_rsun           110
#define  timingfmon           111
#define  timingfmoff           112
#define  fmrecordon           113
#define  fmrecordoff           114
#define  fmtime           115
#define  fm_manual           116
#define  fmautoload           117
#define  fm_switch           118
#define  fm_vol           119
#define  t_cal           120
#define  t_music           121
#define  t_record           122
#define  t_fm           123
#define  t_sys           124
#define  f_browse           125
#define  alarm1           126
#define  t_ebook           127
#define  sel_file           128
#define  play_set           129
#define  handplay           130
#define  autoplay           131
#define  playspeed           132

#define  STRINGID_SUM      132



////BmpResID Define Table////
#define  CH_NOFILE           1
#define  CLOCK           2
#define  CLOCKA           3
#define  CONTRSTW           4
#define  DELLA           5
#define  DIRROOTA           6
#define  EBOOK           7
#define  EBOOKA           8
#define  ENGDELLA           9
#define  EN_NOFILE           10
#define  FM           11
#define  FMA           12
#define  MMP3           13
#define  MUSIC1           14
#define  MUSICA           15
#define  MWAV           16
#define  MWMA           17
#define  NOUDISK           18
#define  PMDSCOM           19
#define  RTC_NUM_0           20
#define  RTC_NUM_1           21
#define  RTC_NUM_2           22
#define  RTC_NUM_3           23
#define  RTC_NUM_4           24
#define  RTC_NUM_5           25
#define  RTC_NUM_6           26
#define  RTC_NUM_7           27
#define  RTC_NUM_8           28
#define  RTC_NUM_9           29
#define  RTC_NUM_A           30
#define  RTC_NUM_B           31
#define  RECORD1           32
#define  RECORDA           33
#define  SDMMC           34
#define  SIGNAL_1           35
#define  SIGNAL_2           36
#define  SIGNAL_3           37
#define  SIGNAL_4           38
#define  SIGNAL_5           39
#define  SIGNAL_6           40
#define  SYSTEM1           41
#define  SYSTEMA           42
#define  UDISK           43
#define  UDISKOUT           44
#define  ACTIVE           45
#define  ALARM           46
#define  ALARM_C           47
#define  ALARM_CA           48
#define  ALARM_O           49
#define  ALARM_OA           50
#define  ALARM_T           51
#define  ALARM_TA           52
#define  ALARM_V           53
#define  ALARM_VA           54
#define  ALMICON           55
#define  BATTERY01           56
#define  BATTERY02           57
#define  BATTERY03           58
#define  BATTERY04           59
#define  BATTERY05           60
#define  BATTERY06           61
#define  BIGBAT1           62
#define  BIGBAT2           63
#define  BIGBAT3           64
#define  BIGBAT4           65
#define  BIGBAT5           66
#define  BKCLR1           67
#define  BPS128           68
#define  BPS160           69
#define  BPS48           70
#define  BPS64           71
#define  BPS96           72
#define  C_NOSEL           73
#define  C_SEL           74
#define  CALENDAR           75
#define  CALENDAR1           76
#define  CALENDAR2           77
#define  CARDOUT           78
#define  CDEL_NO           79
#define  CDEL_YES           80
#define  CHNDEL           81
#define  CHNSAVE           82
#define  CHNWAIT           83
#define  CONTRSTB           84
#define  CYCLE1           85
#define  CYCLE2           86
#define  CYCLE3           87
#define  CYCLE4           88
#define  DELFILE           89
#define  DISCERR           90
#define  EDELFILE           91
#define  EFICON           92
#define  ENGCOUT           93
#define  ENGDEL           94
#define  ENGDICERR           95
#define  ENGNOCARD           96
#define  ENGNODISK           97
#define  ENGNOSPAC           98
#define  ENGSAVE           99
#define  ENGUOUT           100
#define  ENGWAIT           101
#define  ENOFILE           102
#define  EQCLAS           103
#define  EQDBB           104
#define  EQJAZZ           105
#define  EQNOR           106
#define  EQPOP           107
#define  EQROCK           108
#define  EQSOFT           109
#define  EQWOW           110
#define  FLASH           111
#define  FMUSIC           112
#define  FOLDER           113
#define  FOLDERM           114
#define  FQBAR           115
#define  FQBAR2           116
#define  GOODBYTE           117
#define  LARROW           118
#define  LINEIN1           119
#define  LINEIN2           120
#define  LINEINBIG           121
#define  LINEINXXX           122
#define  LOGOFM           123
#define  LOWBAT           124
#define  MICIN           125
#define  MLOOP1           126
#define  MLOOP2           127
#define  MLOOP3           128
#define  MLOOP4           129
#define  MLOOP5           130
#define  MLOOP6           131
#define  MP3_128           132
#define  MP3_16           133
#define  MP3_192           134
#define  MP3_24K           135
#define  MP3_256           136
#define  MP3_32           137
#define  MP3_320           138
#define  MP3_32K           139
#define  MP3_48           140
#define  MP3_48K           141
#define  MP3_64           142
#define  MP3_8           143
#define  MP3_96           144
#define  MREC           145
#define  MWAV128K           146
#define  MWAV176K           147
#define  MWAV192K           148
#define  MWAV256K           149
#define  MWAV32K           150
#define  MWAV353K           151
#define  MWAV384K           152
#define  MWAV64K           153
#define  MWMA128K           154
#define  MWMA176K           155
#define  MWMA192K           156
#define  MWMA32K           157
#define  MWMA64K           158
#define  MWMA96K           159
#define  NO_ICON           160
#define  NOCARD           161
#define  NOFILE           162
#define  NOMEMORY           163
#define  NONGZI           164
#define  OFF_SEL           165
#define  ON_SEL           166
#define  PAUSE           167
#define  PAUSE1           168
#define  RARROW           169
#define  REC_FILE           170
#define  RECSAVE           171
#define  SCANSD1           172
#define  SCANSDMMC           173
#define  SCANUD1           174
#define  SCANUDISK           175
#define  SLIDER           176
#define  SLIDER_P           177
#define  TOOLP           178
#define  TOOLPA           179
#define  UNACTIVE           180
#define  WAV_12K           181
#define  WAV_16K           182
#define  WAV_24K           183
#define  WAV_32K           184
#define  WAV_48K           185
#define  WAV_8K           186

#define  BMPID_SUM           186

#endif
