/*
 * FFT Hardware Test Usage Example Header
 * 
 * Author: Audio Algorithm Test Team
 * Date: 2025-08-06
 * Version: v2.0
 */

#ifndef FFT_TEST_USAGE_EXAMPLE_H
#define FFT_TEST_USAGE_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/*
 * Test result enumeration
 */
typedef enum {
    FFT_TEST_RESULT_PASS = 0,
    FFT_TEST_RESULT_FAIL = 1,
    FFT_TEST_RESULT_WARNING = 2
} fft_test_result_t;

/*
 * Application integration functions
 */

// System initialization with FFT quick test
void app_system_init_with_fft_test(void);

// Main application entry with FFT testing
void app_main_with_fft_tests(void);

/*
 * Test selection functions
 */

// Select appropriate test based on build configuration
void select_appropriate_fft_test(void);

// Complete test workflow with error handling
void complete_fft_test_workflow(void);

/*
 * Specialized test functions
 */

#ifdef DEBUG_MODE
// Comprehensive test for debug mode
void debug_comprehensive_fft_test(void);
#endif

// Production test mode (essential tests only)
void production_fft_test(void);

// AEC specific test for voice applications
void voice_app_aec_test(void);

// Continuous monitoring for long-running systems
void continuous_fft_monitoring(void);

/*
 * Result validation and error handling
 */

// Validate test results and return status
fft_test_result_t validate_fft_test_results(void);

// Handle test failure cases
void handle_fft_test_failure(fft_test_result_t result);

/*
 * Build configuration macros for test selection
 */

// Uncomment based on your build type:
// #define PRODUCTION_BUILD
// #define DEBUG_BUILD  
// #define VOICE_APP_BUILD

/*
 * Configuration parameters
 */

// Test interval for continuous monitoring (in cycles)
#define FFT_TEST_MONITOR_INTERVAL    1000

// Watchdog feeding interval during tests (in ms)
#define FFT_TEST_WATCHDOG_INTERVAL   100

/*
 * Usage examples in comments:
 * 
 * // Example 1: Add to system initialization
 * void system_init(void) {
 *     app_system_init_with_fft_test();
 * }
 * 
 * // Example 2: Use in main loop
 * void main(void) {
 *     app_main_with_fft_tests();
 * }
 * 
 * // Example 3: Production testing
 * void factory_test(void) {
 *     production_fft_test();
 *     fft_test_result_t result = validate_fft_test_results();
 *     handle_fft_test_failure(result);
 * }
 * 
 * // Example 4: Debug mode testing
 * #ifdef DEBUG_MODE
 * void debug_session(void) {
 *     debug_comprehensive_fft_test();
 * }
 * #endif
 */

#ifdef __cplusplus
}
#endif

#endif // FFT_TEST_USAGE_EXAMPLE_H
