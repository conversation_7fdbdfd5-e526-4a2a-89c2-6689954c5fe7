/*
 * FFT Hardware Test Usage Example
 * 
 * Author: Audio Algorithm Test Team
 * Date: 2025-08-06
 * Version: v2.0
 */

#include "system/includes.h"
#include "fft_hardware_main_test.h"

/*
 * Example: How to integrate FFT hardware tests into your application
 */

// Example 1: Quick verification during system initialization
void app_system_init_with_fft_test(void)
{
    log_info("System initializing...");
    
    // Initialize hardware
    // ... your hardware init code ...
    
    // Quick FFT hardware verification
    log_info("Performing FFT hardware quick test...");
    fft_hardware_quick_test();
    
    log_info("System initialization completed");
}

// Example 2: Comprehensive test in debug mode
#ifdef DEBUG_MODE
void debug_comprehensive_fft_test(void)
{
    log_info("=== DEBUG MODE: Comprehensive FFT Test ===");
    
    // Feed watchdog before long test
    clr_wdt();
    
    // Run full test suite
    fft_hardware_test_auto_main();
    
    log_info("=== DEBUG MODE: Test Completed ===");
}
#endif

// Example 3: Production test mode
void production_fft_test(void)
{
    log_info("=== PRODUCTION TEST MODE ===");
    
    // Test key performance metrics only
    log_info("Testing FFT performance...");
    fft_hardware_benchmark_test();
    
    log_info("Testing audio quality...");
    fft_hardware_audio_quality_test();
    
    log_info("Testing error handling...");
    fft_hardware_error_recovery_test();
    
    log_info("=== PRODUCTION TEST COMPLETED ===");
}

// Example 4: AEC specific test for voice applications
void voice_app_aec_test(void)
{
    log_info("=== Voice Application AEC Test ===");
    
    // Test AEC specific FFT functionality
    fft_hardware_aec_specialized_test();
    
    log_info("=== AEC Test Completed ===");
}

// Example 5: Continuous monitoring test (for long-running systems)
void continuous_fft_monitoring(void)
{
    static int test_counter = 0;
    const int TEST_INTERVAL = 1000; // Run test every 1000 cycles
    
    test_counter++;
    
    if (test_counter >= TEST_INTERVAL) {
        log_info("=== Periodic FFT Health Check ===");
        
        // Feed watchdog
        clr_wdt();
        
        // Quick verification
        fft_hardware_quick_test();
        
        test_counter = 0;
        log_info("=== Health Check Completed ===");
    }
}

/*
 * Main application entry point with FFT testing
 */
void app_main_with_fft_tests(void)
{
    log_info("Application starting with FFT hardware tests...");
    
    // System initialization with quick test
    app_system_init_with_fft_test();
    
    // Main application loop
    while (1) {
        // Your main application code here
        // ...
        
        // Optional: Periodic health check
        continuous_fft_monitoring();
        
        // Feed watchdog regularly
        clr_wdt();
        
        // Small delay
        delay_2ms(10);
    }
}

/*
 * Test selection based on build configuration
 */
void select_appropriate_fft_test(void)
{
    #ifdef PRODUCTION_BUILD
        // Production mode: Essential tests only
        production_fft_test();
        
    #elif defined(DEBUG_BUILD)
        // Debug mode: Comprehensive testing
        debug_comprehensive_fft_test();
        
    #elif defined(VOICE_APP_BUILD)
        // Voice application: AEC focused tests
        voice_app_aec_test();
        
    #else
        // Default: Quick verification
        fft_hardware_quick_test();
    #endif
}

/*
 * Test result validation and action
 */
typedef enum {
    FFT_TEST_RESULT_PASS = 0,
    FFT_TEST_RESULT_FAIL = 1,
    FFT_TEST_RESULT_WARNING = 2
} fft_test_result_t;

fft_test_result_t validate_fft_test_results(void)
{
    // This function would analyze test outputs and return status
    // Implementation depends on specific test requirements
    
    log_info("Validating FFT test results...");
    
    // Example validation logic:
    // - Check if all tests completed without errors
    // - Verify performance metrics meet requirements
    // - Ensure audio quality parameters are within spec
    
    // For this example, assume tests passed
    log_info("FFT test validation: PASS");
    return FFT_TEST_RESULT_PASS;
}

/*
 * Error handling for test failures
 */
void handle_fft_test_failure(fft_test_result_t result)
{
    switch (result) {
        case FFT_TEST_RESULT_FAIL:
            log_info("ERROR: FFT hardware test failed!");
            log_info("Action: Check hardware configuration");
            // Could trigger system reset or safe mode
            break;
            
        case FFT_TEST_RESULT_WARNING:
            log_info("WARNING: FFT test showed degraded performance");
            log_info("Action: Continue with monitoring");
            break;
            
        case FFT_TEST_RESULT_PASS:
            log_info("INFO: All FFT tests passed successfully");
            break;
            
        default:
            log_info("ERROR: Unknown test result");
            break;
    }
}

/*
 * Complete test workflow with error handling
 */
void complete_fft_test_workflow(void)
{
    log_info("Starting complete FFT test workflow...");
    
    // Feed watchdog
    clr_wdt();
    
    // Select and run appropriate tests
    select_appropriate_fft_test();
    
    // Validate results
    fft_test_result_t result = validate_fft_test_results();
    
    // Handle results
    handle_fft_test_failure(result);
    
    log_info("FFT test workflow completed");
}
