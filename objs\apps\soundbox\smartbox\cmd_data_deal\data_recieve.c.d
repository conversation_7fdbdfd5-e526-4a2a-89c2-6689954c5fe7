objs/apps/soundbox/smartbox/cmd_data_deal/data_recieve.c.o: \
  apps/soundbox/smartbox/cmd_data_deal/data_recieve.c \
  apps/soundbox/include\smartbox/config.h \
  include_lib/system/generic\typedef.h \
  include_lib/driver/cpu/br23\asm/cpu.h \
  include_lib/driver/cpu/br23\asm/br23.h \
  include_lib/driver/cpu/br23\asm/csfr.h \
  include_lib/driver/cpu/br23\asm/irq.h \
  include_lib/driver/cpu/br23\asm/hwi.h \
  include_lib/system\generic/printf.h \
  include_lib/system/generic/typedef.h include_lib\system/generic/log.h \
  include_lib/system\generic/errno-base.h \
  C:/JL/pi32/pi32v2-include\string.h C:/JL/pi32/pi32v2-include/_ansi.h \
  C:/JL/pi32/pi32v2-include\newlib.h \
  C:/JL/pi32/pi32v2-include\sys/config.h \
  C:/JL/pi32/pi32v2-include\machine/ieeefp.h \
  C:/JL/pi32/pi32v2-include\sys/features.h \
  C:/JL/pi32/pi32v2-include\sys/reent.h \
  C:/JL/pi32/pi32v2-include\sys/_types.h \
  C:/JL/pi32/pi32v2-include\machine/_types.h \
  C:/JL/pi32/pi32v2-include\machine/_default_types.h \
  C:/JL/pi32/pi32v2-include\sys/lock.h \
  C:/JL/pi32/pi32v2-include\sys/cdefs.h \
  C:/JL/pi32/pi32v2-include\sys/string.h \
  C:/JL/pi32/pi32v2-include\strings.h \
  C:/JL/pi32/pi32v2-include\sys/types.h \
  C:/JL/pi32/pi32v2-include\sys/_stdint.h \
  C:/JL/pi32/pi32v2-include\machine/types.h include_lib\system/malloc.h \
  apps/soundbox/include\app_config.h \
  include_lib/driver/cpu/br23\asm/clock_define.h \
  apps/soundbox/board/br23\board_config.h \
  apps/soundbox/board/br23/board_ac6954a_demo/board_ac6954a_demo_cfg.h \
  apps/soundbox/board/br23/board_ac695x_demo/board_ac695x_demo_cfg.h \
  apps/common/usb\usb_std_class_def.h \
  apps/soundbox/board/br23/board_ac6951_kgb_v1/board_ac6951_kgb_cfg.h \
  apps/soundbox/board/br23/board_ac6952e_lighter/board_ac6952e_lighter_cfg.h \
  apps/soundbox/board/br23/board_ac6955f_headset_mono/board_ac6955f_headset_mono_cfg.h \
  apps/soundbox/board/br23/board_ac695x_charging_bin/board_ac695x_charging_bin.h \
  apps/soundbox/board/br23/board_ac695x_btemitter/board_ac695x_btemitter.h \
  apps/soundbox/board/br23/board_ac695x_tws_box/board_ac695x_tws_box.h \
  apps/soundbox/board/br23/board_ac695x_tws/board_ac695x_tws.h \
  apps/soundbox/board/br23/board_ac695x_multimedia_charging_bin/board_ac695x_multimedia_charging_bin.h \
  apps/soundbox/board/br23/board_ac695x_soundcard/board_ac695x_soundcard.h \
  apps/soundbox/board/br23/board_ac695x_smartbox/board_ac695x_smartbox.h \
  apps/soundbox/board/br23/board_ac695x_lcd/board_ac695x_lcd_cfg.h \
  apps/soundbox/board/br23/board_ac695x_cvp_develop/board_ac695x_cvp_develop_cfg.h \
  apps/soundbox/board/br23/board_ac695x_audio_effects/board_ac695x_audio_effects_cfg.h \
  apps/soundbox/board/br23/board_ac695x_megaphone/board_ac695x_megaphone_cfg.h \
  apps/soundbox/board/br23/board_ac6951g/board_ac6951g_cfg.h \
  apps/soundbox/board/br23/board_ac6083a/board_ac6083a_cfg.h \
  apps/soundbox/board/br23/board_ac6083a_iap/board_ac6083a_iap_cfg.h \
  include_lib/btctrler\btcontroller_mode.h \
  apps/soundbox/include/user_cfg_id.h apps/common/usb\usb_common_def.h \
  apps/common/third_party_profile/jieli\le_common.h \
  C:/JL/pi32/pi32v2-include\stdint.h \
  C:/JL/pi32/pi32v2-include\sys/_intsup.h \
  include_lib\btstack/bluetooth.h \
  include_lib/btstack/le/ble_data_types.h \
  include_lib/btstack/le/ble_api.h include_lib\btstack/btstack_typedef.h \
  include_lib/btstack/le/le_user.h include_lib/btstack/le/att.h \
  include_lib/btstack/le/gatt.h include_lib/btstack/le/sm.h \
  include_lib/btstack/btstack_event.h \
  include_lib/btstack/third_party/common\spp_user.h \
  include_lib/btstack/third_party/common\ble_user.h \
  include_lib/btstack/third_party/common\btstack_3th_protocol_user.h \
  include_lib/btstack/third_party/common/spp_config.h \
  include_lib/btstack/third_party/common/ble_config.h \
  include_lib/btstack/third_party/rcsp\JL_rcsp_api.h \
  include_lib/driver/device\uart.h include_lib/system\device/device.h \
  include_lib/system\generic/list.h include_lib/system\generic/atomic.h \
  include_lib/system/generic/cpu.h include_lib/system/generic/irq.h \
  include_lib/system\device/ioctl_cmds.h \
  include_lib/system\generic/ioctl.h include_lib\system/task.h \
  include_lib/system/os/os_api.h include_lib/system\os/os_cpu.h \
  include_lib/system/generic\jiffies.h include_lib/system\os/os_error.h \
  include_lib/system\os/os_type.h \
  include_lib/system/os/FreeRTOS/FreeRTOS.h \
  include_lib/system/os/FreeRTOS/FreeRTOSConfig.h \
  include_lib/system/os/FreeRTOS/pi32v2/portmacro.h \
  include_lib/system/os/FreeRTOS/projdefs.h \
  include_lib/system/os/FreeRTOS/portable.h \
  include_lib/system/os/FreeRTOS/deprecated_definitions.h \
  include_lib/system/os/FreeRTOS/mpu_wrappers.h \
  include_lib/system/os/FreeRTOS/semphr.h \
  include_lib/system/os/FreeRTOS/queue.h \
  include_lib/system/os/FreeRTOS/task.h \
  include_lib/system/os/FreeRTOS/list.h \
  include_lib/btstack/third_party/rcsp\JL_rcsp_protocol.h \
  include_lib/btstack/third_party/rcsp/JL_rcsp_packet.h \
  include_lib/btstack/third_party/rcsp\attr.h \
  apps/soundbox/include\smartbox/event.h \
  apps/soundbox/include\common/user_msg.h
