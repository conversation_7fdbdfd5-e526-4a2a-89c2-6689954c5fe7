/*
 * Minimal FFT Test Program
 * Ultra-lightweight version to avoid system resets
 */

#include "system/includes.h"
#include "hw_fft.h"
#include <string.h>

// Minimal logging
#define TEST_LOG(format, ...)  printf("[FFT] " format "\r\n", ## __VA_ARGS__)

// Real FFT buffer sizes
#define FFT_256_SIZE        256
#define FFT_256_BUF_SIZE    258  // (256/2+1)*2 = 258

// Quick FFT test - minimal resource usage
static int quick_fft_test(void) {
    // Use static buffer to avoid stack overflow
    static int test_buf[FFT_256_BUF_SIZE];
    
    TEST_LOG("Quick 256-point Real FFT test");
    TEST_LOG("Buffer size: %d ints", FFT_256_BUF_SIZE);
    
    // Generate simple test data (sine wave)
    for (int i = 0; i < FFT_256_SIZE; i++) {
        // Simple sine approximation
        if (i < FFT_256_SIZE/4) {
            test_buf[i] = i * 32;
        } else if (i < FFT_256_SIZE/2) {
            test_buf[i] = (FFT_256_SIZE/2 - i) * 32;
        } else if (i < 3*FFT_256_SIZE/4) {
            test_buf[i] = -(i - FFT_256_SIZE/2) * 32;
        } else {
            test_buf[i] = -(FFT_256_SIZE - i) * 32;
        }
    }
    
    // Configure FFT
    unsigned int fft_cfg = hw_fft_config(FFT_256_SIZE, 8, 1, 1, 0);
    
    // Execute FFT
    TEST_LOG("Executing FFT...");

    // Feed watchdog before FFT
    wdt_clear();

    unsigned int start_time = timer_get_ms();
    hw_fft_run(fft_cfg, test_buf, test_buf);
    unsigned int end_time = timer_get_ms();

    // Feed watchdog after FFT
    wdt_clear();
    
    TEST_LOG("FFT completed in %u ms", end_time - start_time);
    
    // Quick IFFT test
    unsigned int ifft_cfg = hw_fft_config(FFT_256_SIZE, 8, 1, 1, 1);

    TEST_LOG("Executing IFFT...");

    // Feed watchdog before IFFT
    wdt_clear();

    start_time = timer_get_ms();
    hw_fft_run(ifft_cfg, test_buf, test_buf);
    end_time = timer_get_ms();

    // Feed watchdog after IFFT
    wdt_clear();
    
    TEST_LOG("IFFT completed in %u ms", end_time - start_time);
    TEST_LOG("Quick test PASSED");
    
    return 1;
}

// Minimal main test function
void fft_test_main(void) {
    TEST_LOG("=== Minimal Real FFT Test ===");
    TEST_LOG("Buffer formula: (N/2+1)*2");
    TEST_LOG("256-point: int tmpbuf[258];");
    
    // Single quick test to avoid system overload
    if (quick_fft_test()) {
        TEST_LOG("SUCCESS: FFT hardware working!");
    } else {
        TEST_LOG("FAILED: FFT test failed");
    }
    
    TEST_LOG("=== Test Complete ===");
    
    // Add small delay to ensure output is flushed
    for (volatile int i = 0; i < 100000; i++);
}
