/*
 * FFT/IFFT 512-point Hardware Acceleration Audio Processing Specialized Test
 * 
 * Test Objectives:
 * 1. 512-point FFT/IFFT precision test, suitable for audio processing range verification
 * 2. Hardware acceleration math function performance test
 * 3. Echo cancellation algorithm application scenario test
 * 4. Audio frequency response analysis
 * 5. Dynamic range and signal-to-noise ratio test
 * 
 * Author: Audio Test Engineer
 * Date: 2025-08-06
 */

#include "system/includes.h"
#include "hw_fft.h"
#include "MathFunc_fix.h"
#include <string.h>
#include <stdlib.h>
#include <math.h>

// 日志输出宏
#define log_info(format, ...)  printf("[FFT_512_AUDIO] " format "\r\n", ## __VA_ARGS__)
#define log_error(format, ...) printf("[FFT_512_ERROR] " format "\r\n", ## __VA_ARGS__)

// FFT 512点配置
#define FFT_512_SIZE        512
#define FFT_512_LOG2        9       // log2(512) = 9
#define FFT_512_BUF_SIZE    514     // (512/2+1)*2 for real FFT
#define COMPLEX_BUF_SIZE    1024    // 512*2 for complex FFT

// 音频处理相关常数
#define AUDIO_SAMPLE_RATE   16000   // 16kHz采样率
#define AUDIO_FRAME_MS      32      // 32ms帧长 (16000*0.032=512采样点)
#define MAX_AUDIO_FREQ      8000    // 最大音频频率 (奈奎斯特频率)
#define FREQ_BIN_SIZE       (AUDIO_SAMPLE_RATE / FFT_512_SIZE)  // 每个频率点 ~31.25Hz

// 测试信号参数
#define TEST_FREQ_1         1000    // 1kHz测试音
#define TEST_FREQ_2         2000    // 2kHz测试音  
#define TEST_FREQ_3         4000    // 4kHz测试音
#define NOISE_LEVEL         100     // 噪声级别

// 精度和动态范围
#define Q15_SCALE           32768   // 2^15 for Q15 format
#define Q24_SCALE           16777216 // 2^24 for Q24 format
#define MIN_SNR_DB          40      // 最小信噪比要求
#define MAX_THD_PERCENT     1.0     // 最大总谐波失真

// 测试结果统计
typedef struct {
    int total_tests;
    int passed_tests;
    int failed_tests;
    
    // 精度测试
    int precision_tests;
    int precision_passed;
    
    // 音频范围测试
    int audio_range_tests;
    int audio_range_passed;
    
    // 性能测试
    int performance_tests;
    int performance_passed;
    
    // 回音消除测试
    int aec_tests;
    int aec_passed;
    
    // 数学加速测试
    int math_tests;
    int math_passed;
} audio_test_results_t;

// 全局测试结果
static audio_test_results_t test_results = {0};

// 测试数据缓冲区 (减少内存使用)
static int input_buffer[COMPLEX_BUF_SIZE] __attribute__((aligned(4)));
static int output_buffer[COMPLEX_BUF_SIZE] __attribute__((aligned(4)));
// 重用output_buffer作为临时缓冲区，减少内存占用
#define reference_buffer output_buffer  // 重用输出缓冲区
#define temp_buffer input_buffer        // 重用输入缓冲区

/*
 * 生成测试音频信号 - 正弦波
 */
static void generate_sine_wave(int *buffer, int length, int frequency, int amplitude)
{
    int i;
    long phase_step = (long)((2.0 * 3.14159265359 * frequency * Q24_SCALE) / AUDIO_SAMPLE_RATE);
    long phase = 0;
    
    for (i = 0; i < length; i++) {
        // 使用硬件加速的sin函数
        long sin_val = sin_fix(phase);
        buffer[i] = (int)((sin_val * amplitude) >> 24);
        
        phase += phase_step;
        if (phase >= (2 * 3.14159265359 * Q24_SCALE)) {
            phase -= (long)(2 * 3.14159265359 * Q24_SCALE);
        }
    }
}

/*
 * 生成复合音频信号（多频率混合）
 */
static void generate_composite_audio(int *buffer, int length)
{
    int temp1[FFT_512_SIZE], temp2[FFT_512_SIZE], temp3[FFT_512_SIZE];
    int i;
    
    // 生成三个不同频率的正弦波
    generate_sine_wave(temp1, length, TEST_FREQ_1, Q15_SCALE/4);    // 1kHz
    generate_sine_wave(temp2, length, TEST_FREQ_2, Q15_SCALE/4);    // 2kHz  
    generate_sine_wave(temp3, length, TEST_FREQ_3, Q15_SCALE/8);    // 4kHz
    
    // 混合信号并添加少量噪声
    for (i = 0; i < length; i++) {
        buffer[i] = temp1[i] + temp2[i] + temp3[i] + (rand() % NOISE_LEVEL - NOISE_LEVEL/2);
        
        // 防止溢出
        if (buffer[i] > Q15_SCALE - 1) buffer[i] = Q15_SCALE - 1;
        if (buffer[i] < -Q15_SCALE) buffer[i] = -Q15_SCALE;
    }
}

/*
 * 计算信号功率
 */
static long calculate_signal_power(int *buffer, int length)
{
    long long power = 0;
    int i;
    
    for (i = 0; i < length; i++) {
        long long sample = buffer[i];
        power += sample * sample;
    }
    
    return (long)(power / length);
}

/*
 * 查找频谱峰值位置
 */
static int find_peak_frequency_bin(int *fft_output, int length)
{
    int max_bin = 0;
    long max_magnitude = 0;
    int i;
    
    // 只检查正频率部分 (DC到奈奎斯特频率)
    for (i = 1; i < length/2; i++) {
        int real = fft_output[i*2];
        int imag = fft_output[i*2 + 1];
        long magnitude = (long)real * real + (long)imag * imag;
        
        if (magnitude > max_magnitude) {
            max_magnitude = magnitude;
            max_bin = i;
        }
    }
    
    return max_bin;
}

/*
 * 测试512点实数FFT基本功能
 */
static int test_real_fft_512_basic(void)
{
    log_info("=== 512-point Real FFT Basic Function Test ===");
    
    // 生成1kHz测试信号
    memset(input_buffer, 0, sizeof(input_buffer));
    generate_sine_wave(input_buffer, FFT_512_SIZE, TEST_FREQ_1, Q15_SCALE/2);
    
    // 配置实数FFT
    unsigned int fft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 0, 1);
    
    // 执行FFT
    memset(output_buffer, 0, sizeof(output_buffer));
    hw_fft_run(fft_config, input_buffer, output_buffer);
    
    // 查找峰值频率
    int peak_bin = find_peak_frequency_bin(output_buffer, FFT_512_BUF_SIZE);
    int detected_freq = peak_bin * FREQ_BIN_SIZE;
    
    log_info("Input frequency: %d Hz, Detected frequency: %d Hz (bin: %d)", 
             TEST_FREQ_1, detected_freq, peak_bin);
    
    // 频率检测精度验证 (允许±1个频率点的误差)
    int freq_error = abs(detected_freq - TEST_FREQ_1);
    int acceptable = (freq_error <= FREQ_BIN_SIZE);
    
    if (acceptable) {
        log_info("✓ Frequency detection accuracy test passed");
        test_results.precision_passed++;
    } else {
        log_error("✗ Frequency detection accuracy test failed, error: %d Hz", freq_error);
    }
    
    test_results.precision_tests++;
    return acceptable;
}

/*
 * 测试512点FFT/IFFT往返精度
 */
static int test_fft_ifft_roundtrip_512(void)
{
    log_info("=== 512-point FFT/IFFT Roundtrip Accuracy Test ===");
    
    // 生成复合音频信号
    memset(input_buffer, 0, sizeof(input_buffer));
    generate_composite_audio(input_buffer, FFT_512_SIZE);
    
    // 备份原始信号
    memcpy(reference_buffer, input_buffer, FFT_512_SIZE * sizeof(int));
    
    // FFT配置
    unsigned int fft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 0, 1);
    unsigned int ifft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 1, 1);
    
    // 执行FFT
    memset(output_buffer, 0, sizeof(output_buffer));
    hw_fft_run(fft_config, input_buffer, output_buffer);
    
    // 执行IFFT
    memset(temp_buffer, 0, sizeof(temp_buffer));
    hw_fft_run(ifft_config, output_buffer, temp_buffer);
    
    // 计算误差
    long max_error = 0;
    long total_error = 0;
    int i;
    
    for (i = 0; i < FFT_512_SIZE; i++) {
        long error = abs(temp_buffer[i] - reference_buffer[i]);
        if (error > max_error) max_error = error;
        total_error += error;
    }
    
    long avg_error = total_error / FFT_512_SIZE;
    double error_percentage = (double)max_error / Q15_SCALE * 100.0;
    
    log_info("Max error: %ld (%d/1000%%), Average error: %ld", 
             max_error, (int)(error_percentage * 1000), avg_error);
    
    // 精度要求：最大误差不超过0.1%
    int acceptable = (error_percentage < 0.1);
    
    if (acceptable) {
        log_info("✓ FFT/IFFT roundtrip accuracy test passed");
        test_results.precision_passed++;
    } else {
        log_error("✗ FFT/IFFT roundtrip accuracy test failed");
    }
    
    test_results.precision_tests++;
    return acceptable;
}

/*
 * 测试音频频率范围覆盖
 */
static int test_audio_frequency_range(void)
{
    log_info("=== Audio Frequency Range Test ===");
    
    int test_frequencies[] = {100, 500, 1000, 2000, 3000, 4000, 6000, 7000};
    int num_freqs = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    int passed = 0;
    int i;
    
    for (i = 0; i < num_freqs; i++) {
        int test_freq = test_frequencies[i];
        
        // 跳过超出奈奎斯特频率的测试
        if (test_freq >= MAX_AUDIO_FREQ) continue;
        
        // 生成测试信号
        memset(input_buffer, 0, sizeof(input_buffer));
        generate_sine_wave(input_buffer, FFT_512_SIZE, test_freq, Q15_SCALE/2);
        
        // 执行FFT
        unsigned int fft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 0, 1);
        memset(output_buffer, 0, sizeof(output_buffer));
        hw_fft_run(fft_config, input_buffer, output_buffer);
        
        // 检测频率
        int peak_bin = find_peak_frequency_bin(output_buffer, FFT_512_BUF_SIZE);
        int detected_freq = peak_bin * FREQ_BIN_SIZE;
        int freq_error = abs(detected_freq - test_freq);
        
        if (freq_error <= FREQ_BIN_SIZE) {
            log_info("✓ %d Hz detection correct (detected: %d Hz)", test_freq, detected_freq);
            passed++;
        } else {
            log_error("✗ %d Hz detection failed (detected: %d Hz)", test_freq, detected_freq);
        }
        
        test_results.audio_range_tests++;
    }
    
    test_results.audio_range_passed += passed;
    
    int success_rate = (passed * 100) / num_freqs;  // 使用整数计算
    log_info("Frequency range test success rate: %d%% (%d/%d)", success_rate, passed, num_freqs);
    
    return (success_rate >= 90);  // 要求90%以上成功率
}

/*
 * 测试数学加速函数性能
 */
static int test_math_acceleration_performance(void)
{
    log_info("=== Math Acceleration Performance Test ===");
    
    int iterations = 1000;
    long start_time, end_time;
    int i;
    
    // 测试sin函数性能
    start_time = timer_get_ms();
    for (i = 0; i < iterations; i++) {
        long angle = (long)((2.0 * 3.14159265359 * i * Q24_SCALE) / iterations);
        volatile long result = sin_fix(angle);
        (void)result; // 避免编译器优化
    }
    end_time = timer_get_ms();
    
    long sin_time = end_time - start_time;
    
    // 测试cos函数性能
    start_time = timer_get_ms();
    for (i = 0; i < iterations; i++) {
        long angle = (long)((2.0 * 3.14159265359 * i * Q24_SCALE) / iterations);
        volatile long result = cos_fix(angle);
        (void)result;
    }
    end_time = timer_get_ms();
    
    long cos_time = end_time - start_time;
    
    // 测试atan函数性能
    start_time = timer_get_ms();
    for (i = 0; i < iterations; i++) {
        long x = 1000 + i;
        long y = 500 + i/2;
        volatile struct data_q_struct result = angle_fix(x, y);
        (void)result;
    }
    end_time = timer_get_ms();
    
    long atan_time = end_time - start_time;
    
    log_info("Math function performance (%d calls):", iterations);
    log_info("  sin_fix: %ld ms (avg %d us)", sin_time, (int)(sin_time * 1000 / iterations));
    log_info("  cos_fix: %ld ms (avg %d us)", cos_time, (int)(cos_time * 1000 / iterations));
    log_info("  angle_fix: %ld ms (avg %d us)", atan_time, (int)(atan_time * 1000 / iterations));
    
    // 性能要求：平均每次调用不超过0.01ms
    int acceptable = (sin_time < iterations/100) && (cos_time < iterations/100) && (atan_time < iterations/10);
    
    if (acceptable) {
        log_info("✓ Math acceleration performance test passed");
        test_results.math_passed++;
    } else {
        log_error("✗ Math acceleration performance test failed");
    }
    
    test_results.math_tests++;
    return acceptable;
}

/*
 * 模拟回音消除场景测试
 */
static int test_aec_scenario(void)
{
    log_info("=== AEC Application Scenario Test ===");
    
    // 模拟远端信号 (扬声器播放的信号)
    int far_end_signal[FFT_512_SIZE];
    generate_sine_wave(far_end_signal, FFT_512_SIZE, TEST_FREQ_1, Q15_SCALE/4);
    
    // 模拟近端信号 (麦克风接收的信号：原始语音 + 回音)
    int near_end_signal[FFT_512_SIZE];
    int echo_signal[FFT_512_SIZE];
    int voice_signal[FFT_512_SIZE];
    
    // 生成回音信号 (远端信号的延迟和衰减版本)
    int echo_delay = 50; // 延迟50个采样点
    memset(echo_signal, 0, sizeof(echo_signal));
    for (int i = echo_delay; i < FFT_512_SIZE; i++) {
        echo_signal[i] = far_end_signal[i - echo_delay] / 4; // 衰减为1/4
    }
    
    // 生成语音信号
    generate_sine_wave(voice_signal, FFT_512_SIZE, TEST_FREQ_2, Q15_SCALE/4);
    
    // 合成近端信号
    for (int i = 0; i < FFT_512_SIZE; i++) {
        near_end_signal[i] = voice_signal[i] + echo_signal[i];
        // 防止溢出
        if (near_end_signal[i] > Q15_SCALE - 1) near_end_signal[i] = Q15_SCALE - 1;
        if (near_end_signal[i] < -Q15_SCALE) near_end_signal[i] = -Q15_SCALE;
    }
    
    // 对远端和近端信号进行FFT分析
    unsigned int fft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 0, 1);
    
    // 远端信号FFT
    memcpy(input_buffer, far_end_signal, FFT_512_SIZE * sizeof(int));
    memset(output_buffer, 0, sizeof(output_buffer));
    hw_fft_run(fft_config, input_buffer, output_buffer);
    memcpy(reference_buffer, output_buffer, FFT_512_BUF_SIZE * sizeof(int));
    
    // 近端信号FFT
    memcpy(input_buffer, near_end_signal, FFT_512_SIZE * sizeof(int));
    memset(output_buffer, 0, sizeof(output_buffer));
    hw_fft_run(fft_config, input_buffer, output_buffer);
    
    // 检测频谱中的回音成分
    int far_end_peak = find_peak_frequency_bin(reference_buffer, FFT_512_BUF_SIZE);
    int near_end_peak = find_peak_frequency_bin(output_buffer, FFT_512_BUF_SIZE);
    
    int far_end_freq = far_end_peak * FREQ_BIN_SIZE;
    int near_end_freq = near_end_peak * FREQ_BIN_SIZE;
    
    log_info("远端主频率: %d Hz (频率点: %d)", far_end_freq, far_end_peak);
    log_info("近端主频率: %d Hz (频率点: %d)", near_end_freq, near_end_peak);
    
    // 计算回音功率比
    int echo_bin_real = output_buffer[far_end_peak * 2];
    int echo_bin_imag = output_buffer[far_end_peak * 2 + 1];
    long echo_power = (long)echo_bin_real * echo_bin_real + (long)echo_bin_imag * echo_bin_imag;
    
    int voice_bin_real = output_buffer[near_end_peak * 2];
    int voice_bin_imag = output_buffer[near_end_peak * 2 + 1];
    long voice_power = (long)voice_bin_real * voice_bin_real + (long)voice_bin_imag * voice_bin_imag;
    
    int echo_to_voice_ratio = (int)((echo_power * 1000) / voice_power); // 使用千分比
    
    log_info("Echo to voice power ratio: %d/1000", echo_to_voice_ratio);
    
    // 回音检测成功的标准
    int echo_detected = (abs(far_end_freq - TEST_FREQ_1) <= FREQ_BIN_SIZE) && 
                       (echo_to_voice_ratio > 10 && echo_to_voice_ratio < 1000);
    
    if (echo_detected) {
        log_info("✓ Echo detection test passed");
        test_results.aec_passed++;
    } else {
        log_error("✗ Echo detection test failed");
    }
    
    test_results.aec_tests++;
    return echo_detected;
}

/*
 * 测试动态范围和噪声容限
 */
static int test_dynamic_range_and_noise(void)
{
    log_info("=== Dynamic Range and Noise Tolerance Test ===");
    
    int test_amplitudes[] = {Q15_SCALE/16, Q15_SCALE/8, Q15_SCALE/4, Q15_SCALE/2};
    int num_amps = sizeof(test_amplitudes) / sizeof(test_amplitudes[0]);
    int passed = 0;
    int i;
    
    for (i = 0; i < num_amps; i++) {
        int amplitude = test_amplitudes[i];
        
        // 生成不同幅度的测试信号
        memset(input_buffer, 0, sizeof(input_buffer));
        generate_sine_wave(input_buffer, FFT_512_SIZE, TEST_FREQ_1, amplitude);
        
        // 添加噪声
        for (int j = 0; j < FFT_512_SIZE; j++) {
            input_buffer[j] += (rand() % (NOISE_LEVEL*2) - NOISE_LEVEL);
            // 防止溢出
            if (input_buffer[j] > Q15_SCALE - 1) input_buffer[j] = Q15_SCALE - 1;
            if (input_buffer[j] < -Q15_SCALE) input_buffer[j] = -Q15_SCALE;
        }
        
        // 执行FFT
        unsigned int fft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 0, 1);
        memset(output_buffer, 0, sizeof(output_buffer));
        hw_fft_run(fft_config, input_buffer, output_buffer);
        
        // 检测频率
        int peak_bin = find_peak_frequency_bin(output_buffer, FFT_512_BUF_SIZE);
        int detected_freq = peak_bin * FREQ_BIN_SIZE;
        int freq_error = abs(detected_freq - TEST_FREQ_1);
        
        int amplitude_db = (int)(20 * log10((double)amplitude / Q15_SCALE) * 10); // 用十分之一dB表示
        
        if (freq_error <= FREQ_BIN_SIZE) {
            log_info("✓ Amplitude %d/10 dB: frequency detection correct", amplitude_db);
            passed++;
        } else {
            log_error("✗ Amplitude %d/10 dB: frequency detection failed", amplitude_db);
        }
    }
    
    test_results.audio_range_passed += passed;
    test_results.audio_range_tests += num_amps;
    
    int success_rate = (passed * 100) / num_amps;
    log_info("Dynamic range test success rate: %d%% (%d/%d)", success_rate, passed, num_amps);
    
    return (success_rate >= 75);  // 要求75%以上成功率
}

/*
 * 性能基准测试
 */
static int test_performance_benchmark(void)
{
    log_info("=== Performance Benchmark Test ===");
    
    int iterations = 100;
    long start_time, end_time;
    int i;
    
    // 准备测试数据
    generate_composite_audio(input_buffer, FFT_512_SIZE);
    
    // 测试FFT性能
    unsigned int fft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 0, 1);
    
    start_time = timer_get_ms();
    for (i = 0; i < iterations; i++) {
        hw_fft_run(fft_config, input_buffer, output_buffer);
    }
    end_time = timer_get_ms();
    
    long fft_time = end_time - start_time;
    
    // 测试IFFT性能
    unsigned int ifft_config = hw_fft_config(FFT_512_SIZE, FFT_512_LOG2, 0, 1, 1);
    
    start_time = timer_get_ms();
    for (i = 0; i < iterations; i++) {
        hw_fft_run(ifft_config, output_buffer, temp_buffer);
    }
    end_time = timer_get_ms();
    
    long ifft_time = end_time - start_time;
    
    int fft_avg_time = (fft_time * 1000) / iterations;  // 微秒
    int ifft_avg_time = (ifft_time * 1000) / iterations; // 微秒
    int total_avg_time = fft_avg_time + ifft_avg_time;
    
    log_info("512-point FFT performance (%d tests):", iterations);
    log_info("  FFT average time: %d us", fft_avg_time);
    log_info("  IFFT average time: %d us", ifft_avg_time);
    log_info("  Roundtrip total time: %d us", total_avg_time);
    
    // 计算实时处理能力
    int frame_time_ms = (FFT_512_SIZE * 1000) / AUDIO_SAMPLE_RATE;  // 32ms for 512 samples at 16kHz
    int cpu_usage = (total_avg_time / 1000) * 100 / frame_time_ms;
    
    log_info("  Audio frame length: %d ms", frame_time_ms);
    log_info("  CPU usage: %d%%", cpu_usage);
    
    // 性能要求：CPU使用率不超过25%
    int acceptable = (cpu_usage < 25);
    
    if (acceptable) {
        log_info("✓ Performance benchmark test passed");
        test_results.performance_passed++;
    } else {
        log_error("✗ Performance benchmark test failed - CPU usage too high");
    }
    
    test_results.performance_tests++;
    return acceptable;
}

/*
 * 打印测试结果摘要
 */
static void print_test_summary(void)
{
    log_info("");
    log_info("========================================");
    log_info("         512-point FFT Audio Test Report");
    log_info("========================================");
    
    log_info("Precision tests: %d/%d passed", test_results.precision_passed, test_results.precision_tests);
    log_info("Audio range tests: %d/%d passed", test_results.audio_range_passed, test_results.audio_range_tests);
    log_info("Performance tests: %d/%d passed", test_results.performance_passed, test_results.performance_tests);
    log_info("AEC tests: %d/%d passed", test_results.aec_passed, test_results.aec_tests);
    log_info("Math acceleration tests: %d/%d passed", test_results.math_passed, test_results.math_tests);
    
    test_results.total_tests = test_results.precision_tests + test_results.audio_range_tests + 
                              test_results.performance_tests + test_results.aec_tests + test_results.math_tests;
    test_results.passed_tests = test_results.precision_passed + test_results.audio_range_passed + 
                               test_results.performance_passed + test_results.aec_passed + test_results.math_passed;
    test_results.failed_tests = test_results.total_tests - test_results.passed_tests;
    
    int success_rate = (test_results.passed_tests * 100) / test_results.total_tests;
    
    log_info("----------------------------------------");
    log_info("Total test items: %d", test_results.total_tests);
    log_info("Passed items: %d", test_results.passed_tests);
    log_info("Failed items: %d", test_results.failed_tests);
    log_info("Success rate: %d%%", success_rate);
    log_info("----------------------------------------");
    
    if (success_rate >= 85) {
        log_info("🎉 Overall test result: Excellent - Suitable for audio processing applications");
    } else if (success_rate >= 70) {
        log_info("✓ Overall test result: Good - Basically meets audio processing requirements");
    } else {
        log_error("⚠ Overall test result: Needs improvement - Not recommended for critical audio processing");
    }
    
    log_info("");
    log_info("Audio processing recommendations:");
    log_info("- Sample rate: 16kHz optimal");
    log_info("- Frame length: 32ms (512 points)");
    log_info("- Frequency range: DC - 8kHz");
    log_info("- Dynamic range: ±16383 (-18dB to full scale)");
    log_info("- Application scenarios: Voice processing, echo cancellation, spectrum analysis");
    log_info("========================================");
}

/*
 * 主测试函数入口
 */
void fft_512_audio_test_main(void)
{
    log_info("Starting 512-point FFT/IFFT audio processing test...");
    log_info("Test configuration:");
    log_info("  FFT size: %d points", FFT_512_SIZE);
    log_info("  Sample rate: %d Hz", AUDIO_SAMPLE_RATE);
    log_info("  Frequency resolution: %d Hz", FREQ_BIN_SIZE);
    log_info("  Audio frame length: %d ms", (FFT_512_SIZE * 1000) / AUDIO_SAMPLE_RATE);
    log_info("");
    
    // 简化测试以避免内存问题
    clr_wdt(); // Feed watchdog
    
    log_info("Executing simplified FFT basic test...");
    
    // 简单的数学函数测试
    long test_angle = Q24_SCALE / 4; // 90度
    long sin_result = sin_fix(test_angle);
    long cos_result = cos_fix(test_angle);
    
    log_info("Math function test:");
    log_info("  sin(90°) = %ld (expected ~%ld)", sin_result, Q24_SCALE);
    log_info("  cos(90°) = %ld (expected ~0)", cos_result);
    
    clr_wdt(); // Feed watchdog
    
    log_info("Simplified 512-point FFT audio test completed!");
}

/*
 * FFT 512点音频性能测试函数
 * 测试FFT处理性能
 */
void fft_512_audio_performance_test(void)
{
    log_info("Executing FFT 512-point audio performance test...");
    clr_wdt(); // Feed watchdog
    
    // 创建测试信号
    int test_audio[512];
    int fft_output[1024];
    
    // 生成1kHz正弦波信号
    for (int i = 0; i < 512; i++) {
        long phase = (i * 1000 * Q24_SCALE) / 16000; // 1kHz @ 16kHz采样率
        long sin_val = sin_fix(phase);
        test_audio[i] = (int)(sin_val * Q15_SCALE / Q24_SCALE);
    }
    
    long start_time = timer_get_ms();
    
    // 执行多次FFT测试
    for (int iter = 0; iter < 50; iter++) {
        hw_fft_run_512(test_audio, fft_output, 0); // FFT
        if ((iter + 1) % 10 == 0) {
            clr_wdt(); // Feed watchdog
        }
    }
    
    long end_time = timer_get_ms();
    long total_time = end_time - start_time;
    
    log_info("Performance test results:");
    log_info("- 50 FFT operations completed in %ld ms", total_time);
    log_info("- Average time per FFT: %d us", (int)((total_time * 1000) / 50));
    log_info("- FFT operations per second: %d", (int)(50000 / total_time));
    log_info("FFT 512-point audio performance test completed");
}

/*
 * FFT 512点音频精度测试函数
 * 测试FFT处理的精度
 */
void fft_512_audio_precision_test(void)
{
    log_info("Executing FFT 512-point audio precision test...");
    clr_wdt(); // Feed watchdog
    
    // 生成精确的1kHz正弦波
    int test_signal[512];
    int fft_output[1024];
    
    for (int i = 0; i < 512; i++) {
        // 精确计算1kHz信号
        long phase = (i * 1000 * Q24_SCALE) / 16000;
        long sin_val = sin_fix(phase);
        test_signal[i] = (int)(sin_val * Q15_SCALE / Q24_SCALE);
    }
    
    // 执行FFT
    hw_fft_run_512(test_signal, fft_output, 0);
    
    // 检查频率精度 - 1kHz应该在bin 32附近
    int target_bin = (1000 * 512) / 16000; // = 32
    long max_magnitude = 0;
    int max_bin = 0;
    
    for (int i = target_bin - 2; i <= target_bin + 2; i++) {
        if (i >= 0 && i < 256) {
            long real = fft_output[i * 2];
            long imag = fft_output[i * 2 + 1];
            long magnitude = (real * real + imag * imag) >> 10; // 避免溢出
            
            if (magnitude > max_magnitude) {
                max_magnitude = magnitude;
                max_bin = i;
            }
        }
    }
    
    // 计算频率误差
    int detected_freq = (max_bin * 16000) / 512;
    int freq_error = detected_freq - 1000;
    
    log_info("Precision test results:");
    log_info("- Target frequency: 1000 Hz");
    log_info("- Detected frequency: %d Hz", detected_freq);
    log_info("- Frequency error: %d Hz (%d/100%%)", freq_error, (freq_error * 100) / 10);
    log_info("- Peak bin: %d (expected: %d)", max_bin, target_bin);
    log_info("FFT 512-point audio precision test completed");
}

/*
 * FFT 512点音频范围测试函数
 * 测试不同频率范围的FFT处理
 */
void fft_512_audio_range_test(void)
{
    log_info("Executing FFT 512-point audio range test...");
    clr_wdt(); // Feed watchdog
    
    // 测试频率列表
    int test_frequencies[] = {100, 500, 1000, 2000, 4000, 6000};
    int num_freqs = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    
    int test_signal[512];
    int fft_output[1024];
    
    for (int f = 0; f < num_freqs; f++) {
        int freq = test_frequencies[f];
        
        // 生成测试频率信号
        for (int i = 0; i < 512; i++) {
            long phase = (i * freq * Q24_SCALE) / 16000;
            long sin_val = sin_fix(phase);
            test_signal[i] = (short)(sin_val * Q15_SCALE / Q24_SCALE);
        }
        
        // 执行FFT
        hw_fft_run_512(test_signal, fft_output, 0);
        
        // 查找峰值频率
        int expected_bin = (freq * 512) / 16000;
        long max_magnitude = 0;
        int max_bin = 0;
        
        for (int i = 1; i < 256; i++) {
            long real = fft_output[i * 2];
            long imag = fft_output[i * 2 + 1];
            long magnitude = (real * real + imag * imag) >> 10;
            
            if (magnitude > max_magnitude) {
                max_magnitude = magnitude;
                max_bin = i;
            }
        }
        
        int detected_freq = (max_bin * 16000) / 512;
        int error = detected_freq - freq;
        
        log_info("Frequency %d Hz: detected %d Hz (bin %d), error %d Hz", 
                freq, detected_freq, max_bin, error);
        
        clr_wdt(); // Feed watchdog
    }
    
    log_info("FFT 512-point audio range test completed");
}
